

on ios safari (iphone 15 pro max):
when starting a stream on the go live page (phantom wallet browser connected wallet):
the video screen with the video preview flickers between setting up stream and another text and cant properly start the stream.

the tip button on mobile is not visible since too high.
when clicking the nft button on mobile it opens the cookie dialogue instead of nft mint. 
also check what happens if clicking tip on mobile.

bonkstream bigger on hero mobile

make the peekaboo always be on the edge of the color change no matter on which display and display size (its mostly working for mobile but on desktop its different everywhere - it should work with 1080p and 4k on desktop)

testing mobile (+wallet txs)

remove all comments
nft mint price back (src/components/stream/MintNFTButton.tsx)
wallet platform change

pitch deck done
loom done (30 intro, 30 bout me, 2 min idea slides, 6 min loom fast forward cuts app, 1 minute business canvas monetization and future)








----
i want to create a product presentation pitch video for investors.  

i wanted to start like this:

hi im shad, living in germany duesseldorf im pursuing many things in my live all involving digital innovation and technical noveltys. 

with a software developer background of more than 15 years im now working in my 9-5 as an it consultant in automation and artificial intelligence, driving digital transformation in process digitalisation, automation, web applications, data and ai.

next to working 9to5 im having a few long term companys im supporting with their it architecture, it strategy and digitalisation of processes. having my small company futurewebservice.de being able to experiment with newest technologies and offer them freelance to new products as a technical partner.
since start of the year i have developed many applications some of them previewed on my website and started participating in a few hackathons in the last months. bonk caught my eye as soon as i saw the announcement my idea dropped and it was clear for me that this is going to be the most complex project ive been working on. so i worked on it every day next to my jobs and finished my vision of the final product. from the idea resulted a product design and styling, from design a user story, from the user story the scope was clear and i began implementing the functions. bonk fits to me. i see bonk goin mainstream independent from crypto individually and want to disrupt a monopolized industry. the video streaming platforms. adapting the idea to a new zeitgeist of fast information for now there is no recording function. so its only possible to see whats happening live. within these streams you can chat with other viewers and the creator who is streaming at the other end. now to the fun part! heres the streamoverview and im going to open a stream. look whose there. now whenever i want to show my love to the creator i can send him directly with a push of a button and a verification a custom amount of bonk to support him. there is no middleman - noone takes a cut - theres only a 5% platform fee - no 40-60% like competitors. next to having the possibility to tip or as we call it now bonk the streamer we have the option to make a snapshot of the stream frame we are viewing right now with push of a button a immediate screen of the image  gets captured and shown to you, you can now mint this image as a nft to your wallet having it memorized forever on the blockchain with the metadata information and link to the video. the price for a nft mint is now fixed to 200000 bonk + solona transaction fees but later on the creator can set his nft prices per stream. here the creator gets 75% of the nft mint price and the platform gets 25%. in the status bar below the video you can see a live updating feed of the likes, views, tips, nfts minted. 
when you now feel like you want to stream its easy as a press of a button. just click go live and enter the stream details, take a look at yourself before starting and lets go. here you see the live stats and the live stream itself with the option to open the link of the stream.