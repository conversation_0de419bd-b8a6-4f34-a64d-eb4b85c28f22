import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import createMiddleware from 'next-intl/middleware';
import { locales, defaultLocale } from './lib/i18n';

// In-memory store for rate limiting
// In production, this should be replaced with Redis or similar for distributed deployments
interface RateLimitStore {
  [ip: string]: {
    count: number;
    resetTime: number;
  };
}

// Separate stores for different endpoint types with smaller memory footprint
const adminApiStore: RateLimitStore = {};
const transactionApiStore: RateLimitStore = {};
const authApiStore: RateLimitStore = {};
const generalApiStore: RateLimitStore = {};

// Configuration for different endpoint types - optimized limits
const rateLimits = {
  admin: { limit: 15, windowSizeInSeconds: 60 }, // Reduced from 20 to 15
  transaction: { limit: 25, windowSizeInSeconds: 60 }, // Reduced from 30 to 25
  auth: { limit: 8, windowSizeInSeconds: 60 }, // Reduced from 10 to 8
  general: { limit: 50, windowSizeInSeconds: 60 }, // Reduced from 60 to 50
};

// Clean up expired entries periodically to prevent memory leaks
setInterval(() => {
  const now = Date.now();
  const stores = [adminApiStore, transactionApiStore, authApiStore, generalApiStore];
  
  stores.forEach(store => {
    Object.keys(store).forEach(ip => {
      if (store[ip].resetTime < now) {
        delete store[ip];
      }
    });
  });
}, 300000); // Clean up every 5 minutes

/**
 * Check if a request exceeds the rate limit
 */
function isRateLimited(
  ip: string,
  store: RateLimitStore,
  limit: number,
  windowSizeInSeconds: number
): boolean {
  const now = Date.now();
  const windowSize = windowSizeInSeconds * 1000;
  
  // Initialize or reset if window has expired
  if (!store[ip] || now > store[ip].resetTime) {
    store[ip] = {
      count: 1,
      resetTime: now + windowSize,
    };
    return false;
  }
  
  // Increment counter
  store[ip].count += 1;
  
  // Check if over limit
  return store[ip].count > limit;
}

/**
 * Get appropriate store and limits based on the request path
 */
function getRateLimitConfig(path: string): { 
  store: RateLimitStore; 
  limit: number; 
  windowSize: number;
} {
  // Admin API endpoints
  if (path.startsWith('/api/admin')) {
    return {
      store: adminApiStore,
      limit: rateLimits.admin.limit,
      windowSize: rateLimits.admin.windowSizeInSeconds,
    };
  }
  
  // Transaction endpoints (tip, mint-moment)
  if (path.startsWith('/api/tip') || path.startsWith('/api/mint-moment')) {
    return {
      store: transactionApiStore,
      limit: rateLimits.transaction.limit,
      windowSize: rateLimits.transaction.windowSizeInSeconds,
    };
  }
  
  // Chat endpoints (higher rate limit for chat messages)
  if (path.includes('/api/stream/chat/send')) {
    return {
      store: transactionApiStore, // Use transaction store for chat sending
      limit: 10, // 10 messages per minute
      windowSize: 60,
    };
  }
  
  // Auth endpoints
  if (path.startsWith('/api/auth')) {
    return {
      store: authApiStore,
      limit: rateLimits.auth.limit,
      windowSize: rateLimits.auth.windowSizeInSeconds,
    };
  }
  
  // General API endpoints
  return {
    store: generalApiStore,
    limit: rateLimits.general.limit,
    windowSize: rateLimits.general.windowSizeInSeconds,
  };
}

/**
 * Get client IP from request
 */
function getIP(request: NextRequest): string {
  // Try to get from X-Forwarded-For header (when behind a proxy like Vercel)
  const forwarded = request.headers.get('x-forwarded-for');
  
  if (forwarded) {
    // The first IP in the list is the original client
    return forwarded.split(',')[0].trim();
  }
  
  // Fallback if ip is not available
  return '127.0.0.1'; // Default to localhost if IP cannot be determined
}

// Create internationalization middleware with simpler configuration
const intlMiddleware = createMiddleware({
  locales,
  defaultLocale,
  // Use 'always' for consistent URL structure
  localePrefix: 'always',
});

/**
 * Main middleware function
 */
export function middleware(request: NextRequest) {
  const { pathname, searchParams } = request.nextUrl; //searchparams for password protection
  // Minimal password protection
  const passwordProtect = process.env.PASSWORD_PROTECT;
  if (passwordProtect && !pathname.startsWith('/api')) {
    const cookie = request.cookies.get('bonkstream_pw')?.value;
    const pw = searchParams.get('pw');
    if (cookie !== passwordProtect) {
      // If password submitted via query, set cookie and redirect to clean URL
      if (pw === passwordProtect) {
        // Determine the appropriate locale for redirect
        const locale = request.cookies.get('NEXT_LOCALE')?.value ||
                      request.headers.get('accept-language')?.split(',')[0]?.substring(0, 2) ||
                      defaultLocale;

        // Ensure locale is supported
        const validLocale = locales.includes(locale as (typeof locales)[number]) ? locale : defaultLocale;

        // Create clean URL without password parameter
        const cleanUrl = new URL(`/${validLocale}`, request.url);

        // Create redirect response with cookie
        const response = NextResponse.redirect(cleanUrl);
        response.cookies.set('bonkstream_pw', passwordProtect, { httpOnly: true, maxAge: 60 * 60 * 24 });
        return response;
      }
      // Show minimal password form
      return new NextResponse(`<!DOCTYPE html><html><head><title>Password Required</title></head><body style='font-family:sans-serif;display:flex;flex-direction:column;align-items:center;justify-content:center;height:100vh;'><form method='GET'><input type='password' name='pw' placeholder='Password' style='padding:8px;font-size:1rem;'/><button type='submit' style='margin-left:8px;padding:8px 16px;'>Enter</button></form></body></html>`, {
        status: 401,
        headers: { 'Content-Type': 'text/html' }
      });
    }
  }
  //password protection end
  // Check if the path directly starts with /admin (no locale prefix)
  // This handles direct navigation to /admin paths
  if (pathname.startsWith('/admin') && !pathname.match(/^\/[a-z]{2}\/admin/)) {
    // Determine the locale to use (from cookie, accept-language, or default)
    const locale = request.cookies.get('NEXT_LOCALE')?.value || 
                  request.headers.get('accept-language')?.split(',')[0]?.substring(0, 2) || 
                  defaultLocale;
                  
    // Only redirect if the locale is one of our supported locales
    if (locales.includes(locale as (typeof locales)[number])) {
      // Create a new URL with the locale prefix
      const url = new URL(`/${locale}${pathname}`, request.url);
      url.search = request.nextUrl.search;
      return NextResponse.redirect(url);
    }
  }
  
  // Apply rate limiting only to API routes
  if (pathname.startsWith('/api')) {
    // Skip rate limiting in development mode
    if (process.env.NODE_ENV !== 'development') {
      const ip = getIP(request);
      
      // Get the appropriate rate limit configuration
      const { store, limit, windowSize } = getRateLimitConfig(pathname);
      
      // Check if rate limited
      if (isRateLimited(ip, store, limit, windowSize)) {
        // Return 429 Too Many Requests
        return new NextResponse(
          JSON.stringify({
            error: 'Rate limit exceeded',
            message: 'Too many requests, please try again later.'
          }),
          {
            status: 429,
            headers: {
              'Content-Type': 'application/json',
              // Add headers for retry-after
              'Retry-After': `${windowSize}`,
              // Add CORS headers if needed
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            }
          }
        );
      }
    }
    
    // Continue with the API request (no i18n for API routes)
    return NextResponse.next();
  }
  
  // Apply internationalization middleware to non-API routes
  return intlMiddleware(request);
}

/**
 * Configure which paths this middleware will run on
 */
export const config = {
  matcher: [
    // Apply to all routes except static assets and common file extensions
    '/((?!_next|static|favicon.ico|robots.txt|.*\\.png|.*\\.jpg|.*\\.jpeg|.*\\.gif|.*\\.svg|.*\\.webp|.*\\.ico|.*\\.ttf|.*\\.woff|.*\\.woff2).*)'
  ],
};