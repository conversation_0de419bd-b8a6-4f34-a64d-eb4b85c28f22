/* eslint-disable react-hooks/exhaustive-deps */

import { useEffect, useRef, useState } from 'react';

interface UseViewerTrackingProps {
  streamId: string;
  enabled?: boolean;
}

interface ViewerTrackingResult {
  viewerCount: number;
  viewerId: string | null;
  isTracking: boolean;
}

export function useViewerTracking({ 
  streamId, 
  enabled = true 
}: UseViewerTrackingProps): ViewerTrackingResult {
  const [viewerCount, setViewerCount] = useState(0);
  const [viewerId, setViewerId] = useState<string | null>(null);
  const [isTracking, setIsTracking] = useState(false);
  const trackingRef = useRef<boolean>(false);
  const heartbeatInterval = useRef<NodeJS.Timeout | null>(null);

  // Join viewer tracking when component mounts
  useEffect(() => {
    if (!enabled || !streamId || trackingRef.current) return;

    const joinTracking = async () => {
      try {
        const response = await fetch(`/${window.location.pathname.split('/')[1]}/api/stream/viewer`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            streamId,
            action: 'join'
          }),
        });

        if (response.ok) {
          const data = await response.json();
          setViewerCount(data.viewerCount);
          setViewerId(data.viewerId);
          setIsTracking(true);
          trackingRef.current = true;
          
          
          // Set up heartbeat to maintain viewer presence
          startHeartbeat(data.viewerId);
        } else {
          console.warn('Failed to join viewer tracking:', response.status);
        }
      } catch (error) {
        console.error('Error joining viewer tracking:', error);
      }
    };

    joinTracking();

    // Cleanup on unmount
    return () => {
      if (trackingRef.current) {
        leaveTracking();
      }
    };
  }, [streamId, enabled]);

  // Start heartbeat to maintain viewer presence
  const startHeartbeat = (currentViewerId: string) => {
    // Send heartbeat every 45 seconds to maintain presence (increased from 30s to reduce server load)
    heartbeatInterval.current = setInterval(async () => {
      try {
        const response = await fetch(`/${window.location.pathname.split('/')[1]}/api/stream/viewer`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            streamId,
            action: 'join', // Rejoin to refresh presence
            viewerId: currentViewerId
          }),
        });

        if (response.ok) {
          const data = await response.json();
          setViewerCount(data.viewerCount);
        }
      } catch (error) {
        console.error('Heartbeat error:', error);
      }
    }, 45000); // Increased from 30 to 45 seconds to reduce server load
  };

  // Leave viewer tracking
  const leaveTracking = async () => {
    if (!trackingRef.current || !viewerId) return;

    try {
      await fetch(`/${window.location.pathname.split('/')[1]}/api/stream/viewer`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          streamId,
          action: 'leave',
          viewerId
        }),
      });

      // Clear heartbeat
      if (heartbeatInterval.current) {
        clearInterval(heartbeatInterval.current);
        heartbeatInterval.current = null;
      }

      trackingRef.current = false;
      setIsTracking(false);
    } catch (error) {
      console.error('Error leaving viewer tracking:', error);
    }
  };

  // Handle page visibility changes (pause tracking when tab is hidden)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Page is hidden, leave tracking
        if (trackingRef.current) {
          leaveTracking();
        }
      } else {
        // Page is visible again, rejoin tracking
        if (!trackingRef.current && enabled && streamId) {
          // Re-trigger join tracking
          window.location.reload(); // Simple approach to rejoin
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [enabled, streamId]);

  // Handle beforeunload to leave tracking
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (trackingRef.current && viewerId) {
        // Use sendBeacon for reliable cleanup on page unload
        navigator.sendBeacon(`/${window.location.pathname.split('/')[1]}/api/stream/viewer`, JSON.stringify({
          streamId,
          action: 'leave',
          viewerId
        }));
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [streamId, viewerId]);

  return {
    viewerCount,
    viewerId,
    isTracking
  };
}
