/**
 * Cookie management utilities
 */

export type CookieCategory = 'essential' | 'preferences' | 'analytics';

export type CookiePreferences = {
  essential: boolean;
  preferences: boolean;
  analytics: boolean;
};

// Default cookie preferences (essential only)
const DEFAULT_PREFERENCES: CookiePreferences = {
  essential: true, // Essential cookies are always enabled
  preferences: false,
  analytics: false,
};

/**
 * Get the current cookie consent preferences
 */
export function getCookiePreferences(): CookiePreferences {
  if (typeof window === 'undefined') {
    return DEFAULT_PREFERENCES;
  }

  try {
    const savedPreferences = localStorage.getItem('cookie-consent');
    
    if (!savedPreferences) {
      return DEFAULT_PREFERENCES;
    }
    
    // Additional validation to make sure it's a valid JSON string
    if (!savedPreferences.startsWith('{') || !savedPreferences.endsWith('}')) {
      // Invalid JSON format, reset and return defaults
      localStorage.removeItem('cookie-consent');
      return DEFAULT_PREFERENCES;
    }
    
    const preferences = JSON.parse(savedPreferences);
    
    // Validate structure of parsed preferences
    if (typeof preferences !== 'object' || preferences === null) {
      localStorage.removeItem('cookie-consent');
      return DEFAULT_PREFERENCES;
    }
    
    return {
      ...DEFAULT_PREFERENCES,
      ...preferences,
    };
  } catch (error) {
    console.error('Error reading cookie preferences:', error);
    // Clear the invalid data
    localStorage.removeItem('cookie-consent');
    return DEFAULT_PREFERENCES;
  }
}

/**
 * Check if a specific cookie category is allowed
 */
export function isCookieCategoryAllowed(category: CookieCategory): boolean {
  if (category === 'essential') {
    return true; // Essential cookies are always allowed
  }
  
  const preferences = getCookiePreferences();
  return preferences[category] === true;
}

/**
 * Set a cookie if the category is allowed
 */
export function setCookieIfAllowed(
  name: string,
  value: string,
  category: CookieCategory,
  options: { expires?: Date | number; path?: string; domain?: string; secure?: boolean; sameSite?: 'strict' | 'lax' | 'none' } = {}
): boolean {
  if (!isCookieCategoryAllowed(category)) {
    return false;
  }
  
  let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`;
  
  if (options.expires) {
    if (typeof options.expires === 'number') {
      const date = new Date();
      date.setTime(date.getTime() + options.expires * 24 * 60 * 60 * 1000);
      options.expires = date;
    }
    cookieString += `;expires=${options.expires.toUTCString()}`;
  }
  
  if (options.path) {
    cookieString += `;path=${options.path}`;
  } else {
    cookieString += `;path=/`;
  }
  
  if (options.domain) {
    cookieString += `;domain=${options.domain}`;
  }
  
  if (options.secure || options.sameSite === 'none') {
    cookieString += `;secure`;
  }
  
  if (options.sameSite) {
    cookieString += `;samesite=${options.sameSite}`;
  } else {
    cookieString += `;samesite=lax`;
  }
  
  if (typeof document !== 'undefined') {
    document.cookie = cookieString;
    return true;
  }
  
  return false;
}

/**
 * Get a cookie value by name
 */
export function getCookie(name: string): string | null {
  if (typeof document === 'undefined') {
    return null;
  }
  
  const cookies = document.cookie.split(';');
  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i].trim();
    const [cookieName, cookieValue] = cookie.split('=');
    
    if (decodeURIComponent(cookieName) === name) {
      return decodeURIComponent(cookieValue);
    }
  }
  
  return null;
}

/**
 * Delete a cookie by name
 */
export function deleteCookie(name: string, options: { path?: string; domain?: string } = {}): void {
  if (typeof document === 'undefined') {
    return;
  }
  
  let cookieString = `${encodeURIComponent(name)}=;expires=Thu, 01 Jan 1970 00:00:00 GMT`;
  
  if (options.path) {
    cookieString += `;path=${options.path}`;
  } else {
    cookieString += `;path=/`;
  }
  
  if (options.domain) {
    cookieString += `;domain=${options.domain}`;
  }
  
  document.cookie = cookieString;
}

/**
 * Delete cookies by prefix
 */
export function deleteCookiesByPrefix(prefixes: string[], options: { path?: string; domain?: string } = {}): void {
  if (typeof document === 'undefined') {
    return;
  }
  
  const cookies = document.cookie.split(';');
  
  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i].trim();
    const cookieName = cookie.split('=')[0];
    
    for (const prefix of prefixes) {
      if (cookieName.startsWith(prefix)) {
        deleteCookie(cookieName, options);
      }
    }
  }
}

/**
 * Apply cookie preferences (delete cookies for disabled categories)
 */
export function applyCookiePreferences(preferences: CookiePreferences): void {
  if (!preferences.analytics) {
    // Delete analytics cookies
    deleteCookiesByPrefix(['_va_', '_plausible_']);
    
    // Also clear localStorage items used by analytics
    if (typeof window !== 'undefined') {
      const analyticsKeys = ['_va_', '_plausible_'];
      
      Object.keys(localStorage).forEach(key => {
        analyticsKeys.forEach(prefix => {
          if (key.startsWith(prefix)) {
            localStorage.removeItem(key);
          }
        });
      });
    }
  }
  
  if (!preferences.preferences) {
    // Delete preference cookies
    deleteCookiesByPrefix(['theme', 'language']);
  }
}

/**
 * Check if cookie consent has been given
 */
export function hasGivenCookieConsent(): boolean {
  if (typeof window === 'undefined') {
    return false;
  }
  
  return localStorage.getItem('cookie-consent') !== null;
}

/**
 * Get a user-friendly cookie category name
 */
export function getCookieCategoryName(category: CookieCategory): string {
  switch (category) {
    case 'essential':
      return 'Essential';
    case 'preferences':
      return 'Preferences';
    case 'analytics':
      return 'Analytics';
    default:
      return category;
  }
} 