/**
 * A simple in-memory cache implementation
 * This helps avoid infinite loops with cookie-based authentication
 */
export class Cache<T> {
  private cache: Map<string, { value: T | null; expires?: number }>;
  private defaultTtl?: number; // Default TTL in milliseconds
  private debug: boolean;

  constructor(options?: { defaultTtl?: number; debug?: boolean }) {
    this.cache = new Map();
    this.defaultTtl = options?.defaultTtl;
    this.debug = options?.debug || false;
  }

  /**
   * Gets a value from the cache
   * @param key The cache key
   * @returns The cached value or undefined if not found
   */
  get(key: string): T | null | undefined {
    const item = this.cache.get(key);
    
    // Return undefined if not in cache
    if (!item) {
      if (this.debug) console.debug(`Cache miss: ${key}`);
      return undefined;
    }
    
    // Check if expired
    if (item.expires && item.expires < Date.now()) {
      if (this.debug) console.debug(`Cache expired: ${key}`);
      this.cache.delete(key);
      return undefined;
    }
    
    if (this.debug) console.debug(`Cache hit: ${key}`);
    return item.value;
  }

  /**
   * Sets a value in the cache
   * @param key The cache key
   * @param value The value to cache
   * @param ttlMs Time to live in milliseconds (optional)
   */
  set(key: string, value: T | null, ttlMs?: number): void {
    // Use provided TTL, default TTL, or undefined (no expiration)
    const actualTtl = ttlMs !== undefined ? ttlMs : this.defaultTtl;
    const expires = actualTtl ? Date.now() + actualTtl : undefined;
    
    if (this.debug) {
      console.debug(`Cache set: ${key}, expires: ${expires ? new Date(expires).toISOString() : 'never'}`);
    }
    
    this.cache.set(key, { value, expires });
  }

  /**
   * Removes a value from the cache
   * @param key The cache key to remove
   */
  delete(key: string): void {
    if (this.debug) console.debug(`Cache delete: ${key}`);
    this.cache.delete(key);
  }

  /**
   * Clears all items from the cache
   */
  clear(): void {
    if (this.debug) console.debug('Cache cleared');
    this.cache.clear();
  }
  
  /**
   * Gets the current state of the cache (for debugging)
   * @returns A string representation of the cache
   */
  debugState(): string {
    const now = Date.now();
    const entries = Array.from(this.cache.entries()).map(([key, item]) => {
      const expires = item.expires 
        ? `expires in ${Math.round((item.expires - now) / 1000)}s` 
        : 'never expires';
      return `${key}: ${expires}`;
    });
    
    return `Cache (${entries.length} items):\n${entries.join('\n')}`;
  }
} 