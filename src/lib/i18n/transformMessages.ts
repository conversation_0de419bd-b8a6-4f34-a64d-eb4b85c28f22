import { set } from 'lodash';

/**
 * Transform a flat message object with dot notation keys into a nested object
 * @param messages Object with flat keys like 'common.welcome'
 * @returns Object with nested structure
 */
export function transformFlatMessages(messages: Record<string, string>): Record<string, unknown> {
  return Object.entries(messages).reduce(
    (acc, [key, value]) => set(acc, key, value),
    {}
  );
}

/**
 * Transform the legacy-format messages into next-intl compatible format
 * @param messages Legacy format messages
 * @returns Transformed messages
 */
export function transformMessages(messages: Record<string, string>): Record<string, unknown> {
  // First check if messages are already in the right format
  const hasNestedStructure = Object.keys(messages).some(key => 
    typeof messages[key] === 'object' && messages[key] !== null
  );
  
  if (hasNestedStructure) {
    // Already in the right format
    return messages;
  }
  
  // Check if keys contain dots, indicating a flat structure
  const hasDotNotation = Object.keys(messages).some(key => key.includes('.'));
  
  if (hasDotNotation) {
    // Transform flat messages to nested
    return transformFlatMessages(messages);
  }
  
  // Return as-is if no transformation needed
  return messages;
} 