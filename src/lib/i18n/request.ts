import { getRequestConfig } from 'next-intl/server';
import { locales, defaultLocale } from './index';
import type { Locale } from './index';

// Define message type to make TypeScript happy
interface MessageNamespace {
  [key: string]: unknown;
}

interface Messages {
  common: MessageNamespace;
  admin: MessageNamespace;
  stream: MessageNamespace;
  streaming: MessageNamespace;
  profile: MessageNamespace;
  collection: MessageNamespace;
  creator: MessageNamespace;
  creatorPublicPage: MessageNamespace;
  legal: MessageNamespace;
}

// Load all message namespaces
async function getAllMessages(locale: string): Promise<Messages> {
  try {
    // Load all available namespaces
    const [
      common,
      admin,
      stream,
      streaming,
      profile,
      collection,
      creator,
      creatorPublicPage,
      legal
    ] = await Promise.all([
      import(`../../locales/${locale}/common.json`).then(m => m.default).catch(() => ({})),
      import(`../../locales/${locale}/admin.json`).then(m => m.default).catch(() => ({})),
      import(`../../locales/${locale}/stream.json`).then(m => m.default).catch(() => ({})),
      import(`../../locales/${locale}/streaming.json`).then(m => m.default).catch(() => ({})),
      import(`../../locales/${locale}/profile.json`).then(m => m.default).catch(() => ({})),
      import(`../../locales/${locale}/collection.json`).then(m => m.default).catch(() => ({})),
      import(`../../locales/${locale}/creator.json`).then(m => m.default).catch(() => ({})),
      import(`../../locales/${locale}/creatorPublicPage.json`).then(m => m.default).catch(() => ({})),
      import(`../../locales/${locale}/legal.json`).then(m => m.default).catch(() => ({}))
    ]);
    
    return {
      common,
      admin,
      stream,
      streaming,
      profile,
      collection,
      creator,
      creatorPublicPage,
      legal
    };
  } catch (error) {
    console.error(`Error loading messages for ${locale}:`, error);
    // Return empty objects for all namespaces if loading fails
    return {
      common: {},
      admin: {},
      stream: {},
      streaming: {},
      profile: {},
      collection: {},
      creator: {},
      creatorPublicPage: {},
      legal: {}
    };
  }
}

export default getRequestConfig(async ({ locale }) => {
  // Ensure locale is a valid string that we support
  const safeLocale: string = typeof locale === 'string' && locales.includes(locale as Locale) 
    ? locale 
    : defaultLocale;
  
  // Load all messages for the locale
  const messages = await getAllMessages(safeLocale);
  
  return {
    messages,
    locale: safeLocale,
    timeZone: 'Europe/Berlin'
  };
}); 