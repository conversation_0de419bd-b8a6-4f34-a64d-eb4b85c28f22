import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from './types';
import { Cache } from '@/lib/cache';

type SupabaseClient = ReturnType<typeof createServerClient<Database>>;

// Global singleton interface
interface SupabaseClientSingleton {
  instance: SupabaseClient | null;
  promise: Promise<SupabaseClient> | null;
  timestamp: number;
}

// Augment global type
declare global {
  var supabaseClientSingleton: SupabaseClientSingleton | undefined;
}

// Enable debug mode in development
const isDebug = process.env.NODE_ENV === 'development';

// Create a cache to store cookie values with a 5 minute TTL
const cookieCache = new Cache<string | null>({
  defaultTtl: 5 * 60 * 1000, // 5 minutes
  debug: isDebug
});

// Initialize the global singleton if it doesn't exist
if (!global.supabaseClientSingleton) {
  global.supabaseClientSingleton = {
    instance: null,
    promise: null,
    timestamp: Date.now(),
  };
}

// Create a server-side Supabase client using cookies
export async function createClient(): Promise<SupabaseClient> {
  // If we already have a client promise, return it to avoid multiple simultaneous initializations
  if (global.supabaseClientSingleton!.promise) {
    try {
      // Use the existing promise with timeout
      const client = await Promise.race([
        global.supabaseClientSingleton!.promise,
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Client creation timeout')), 10000)
        )
      ]);
      // Validate client is still usable
      if (!client) throw new Error('Client is null');
      return client;
    } catch (error) {
      // If promise fails, reset it and continue with creation
      console.error('Error waiting for existing client promise:', error);
      global.supabaseClientSingleton!.promise = null;
    }
  }
  
  // If we have a valid instance already, return it
  if (global.supabaseClientSingleton!.instance) {
    // If the client is less than 5 minutes old, reuse it
    const age = Date.now() - global.supabaseClientSingleton!.timestamp;
    if (age < 5 * 60 * 1000) { // 5 minutes
      return global.supabaseClientSingleton!.instance;
    }
    
    // Otherwise reset the instance for a fresh connection
    global.supabaseClientSingleton!.instance = null;
  }
  
  // Create a promise that resolves to the client
  global.supabaseClientSingleton!.promise = (async () => {
    try {
      // Validate environment variables
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
      
      if (!supabaseUrl || !supabaseKey) {
        throw new Error('Missing Supabase environment variables');
      }
      
      if (!supabaseUrl.startsWith('https://')) {
        throw new Error('Invalid Supabase URL format');
      }
            
      // Next.js 15 requires cookies() to be awaited
      const cookieStore = await cookies();
      
      // Create a map to gather all cookies in a single iteration
      const cookiesToCache = new Map<string, string | null>();
      
      // Get the known base cookie (required for prefetching)
      const baseAuthCookie = await cookieStore.get('sb-bsrqpfqwqltnzmtvalwk-auth-token');
      const baseAuthValue = baseAuthCookie?.value ?? null;
      cookiesToCache.set('sb-bsrqpfqwqltnzmtvalwk-auth-token', baseAuthValue);
      
      // Prefetch all chunked cookies in one go
      if (baseAuthValue !== null) {
        // Fetch chunks 0-4 simultaneously
        const cookiePromises = Array.from({ length: 5 }, async (_, i) => {
          try {
            const cookie = await cookieStore.get(`sb-bsrqpfqwqltnzmtvalwk-auth-token.${i}`);
            cookiesToCache.set(`sb-bsrqpfqwqltnzmtvalwk-auth-token.${i}`, cookie?.value ?? null);
          } catch {
            cookiesToCache.set(`sb-bsrqpfqwqltnzmtvalwk-auth-token.${i}`, null);
          }
        });
        
        await Promise.all(cookiePromises);
      }
      
      // Store all cookies in the cache
      for (const [name, value] of cookiesToCache.entries()) {
        cookieCache.set(name, value);
      }
      
      // Create single instance of the client
      const client = createServerClient<Database>(
        supabaseUrl,
        supabaseKey,
        {
          cookies: {
            getAll() {
              return cookieStore.getAll();
            },
            setAll(cookiesToSet) {
              try {
                cookiesToSet.forEach(({ name, value, options }) => {
                  // Update the cache
                  cookieCache.set(name, value);
                  cookieStore.set(name, value, options as CookieOptions);
                });
              } catch (error) {
                console.error('Error setting cookies:', error);
              }
            },
          },
        }
      );
      
      // Store the instance and update timestamp
      global.supabaseClientSingleton!.instance = client;
      global.supabaseClientSingleton!.timestamp = Date.now();
      
      return client;
    } catch (error) {
      console.error('Error creating Supabase client:', error);
      // Clear the promise so we can try again
      global.supabaseClientSingleton!.promise = null;
      throw error;
    } finally {
      // Always clear the promise when done
      setTimeout(() => {
        global.supabaseClientSingleton!.promise = null;
      }, 1000);
    }
  })();
  
  try {
    return await global.supabaseClientSingleton!.promise;
  } catch (error) {
    console.error('Error getting Supabase client:', error);
    // Reset state and throw error instead of returning null
    global.supabaseClientSingleton!.promise = null;
    global.supabaseClientSingleton!.instance = null;
    throw new Error('Failed to create Supabase client');
  }
}

// For server actions
export async function createActionClient() {
  'use server';
  
  return createClient();
}

// Create a service role client that bypasses RLS (for server operations)
export function createServiceClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Missing Supabase service role environment variables');
  }
  
  return createServerClient<Database>(
    supabaseUrl,
    supabaseServiceKey,
    {
      cookies: {
        getAll() {
          return [];
        },
        setAll() {
          // No-op for service client
        },
      },
    }
  );
}