'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useWallet } from '@solana/wallet-adapter-react';
import { But<PERSON> } from '@/components/ui/button';
import Image from 'next/image';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Video, ListFilter, MoreHorizontal, Trash2 } from 'lucide-react';
import { 
    DropdownMenu, 
    DropdownMenuTrigger, 
    DropdownMenuContent, 
    DropdownMenuLabel, 
    DropdownMenuSeparator, 
    DropdownMenuCheckboxItem,
    DropdownMenuItem
} from '@/components/ui/dropdown-menu';

interface Stream {
  id: string;
  title: string;
  status: string; // e.g., 'live', 'recorded', 'processing', 'archived'
  views?: number;
  created_at: string;
  playback_url?: string;
  thumbnail_url?: string;
  duration?: number; // in seconds
  is_actually_live?: boolean;
}

interface DashboardContentProps {
  streams: Stream[];
  locale: string;
  onStartNewStream: () => void; // Callback for starting a new stream
  onStreamDeleted?: (streamId: string) => void; // Callback when stream is deleted
}

const formatDate = (dateString: string, locale: string) => {
  return new Date(dateString).toLocaleDateString(locale, {
    year: 'numeric', month: 'short', day: 'numeric'
  });
};

export function DashboardContent({ streams, locale, onStreamDeleted }: DashboardContentProps) {
  const t = useTranslations('creator.dashboard.content');
  const { publicKey } = useWallet();
  const [filterStatus, setFilterStatus] = useState<string>('all'); // 'all', 'live', 'recorded', 'archived'
  const [deleteStreamId, setDeleteStreamId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const filteredStreams = streams.filter(stream => {
    if (filterStatus === 'all') return true;
    return stream.status.toLowerCase() === filterStatus;
  });

  const getStatusBadgeVariant = (status: string, isActuallyLive?: boolean) => {
    if (status.toLowerCase() === 'live' && !isActuallyLive) {
      return 'outline'; // Show as inactive if marked live but not actually live
    }
    switch (status.toLowerCase()) {
      case 'live': return 'destructive'; // Red for live
      case 'recorded': return 'secondary';
      case 'processing': return 'outline';
      default: return 'default';
    }
  };

  const getStatusDisplayText = (status: string, isActuallyLive?: boolean) => {
    if (status.toLowerCase() === 'live' && isActuallyLive) {
      return 'LIVE';
    }
    if (status.toLowerCase() === 'live' && !isActuallyLive) {
      return 'ENDED';
    }
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const handleDeleteStream = async () => {
    if (!deleteStreamId || !publicKey) return;

    setIsDeleting(true);
    try {
      const walletAddress = publicKey.toString();

      const response = await fetch(`/${locale}/api/streams/${deleteStreamId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          walletAddress: walletAddress
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete stream');
      }

      // Notify parent component of successful deletion
      onStreamDeleted?.(deleteStreamId);
      setDeleteStreamId(null);
    } catch (error) {
      console.error('Error deleting stream:', error);
      alert(`Error: ${(error as Error).message}`);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-white">{t('title')}</h2>
        <div className="flex items-center space-x-2">
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="h-9 gap-1 border-gray-600 hover:bg-gray-700">
                        <ListFilter className="h-3.5 w-3.5" />
                        <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">{t('filter.label')}</span>
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="bg-gray-800 border-gray-700 text-white">
                    <DropdownMenuLabel className="text-gray-300">{t('filter.statusTitle')}</DropdownMenuLabel>
                    <DropdownMenuSeparator className="bg-gray-700"/>
                    <DropdownMenuCheckboxItem 
                        checked={filterStatus === 'all'} 
                        onCheckedChange={() => setFilterStatus('all')} 
                        className="focus:bg-gray-700"
                    >
                        {t('filter.all')}
                    </DropdownMenuCheckboxItem>
                    <DropdownMenuCheckboxItem 
                        checked={filterStatus === 'live'} 
                        onCheckedChange={() => setFilterStatus('live')} 
                        className="focus:bg-gray-700"
                    >
                        {t('filter.live')}
                    </DropdownMenuCheckboxItem>
                    <DropdownMenuCheckboxItem 
                        checked={filterStatus === 'recorded'} 
                        onCheckedChange={() => setFilterStatus('recorded')} 
                        className="focus:bg-gray-700"
                    >
                        {t('filter.recorded')}
                    </DropdownMenuCheckboxItem>
                     <DropdownMenuCheckboxItem 
                        checked={filterStatus === 'archived'} 
                        onCheckedChange={() => setFilterStatus('archived')} 
                        className="focus:bg-gray-700"
                    >
                        {t('filter.archived')}
                    </DropdownMenuCheckboxItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </div>      
      </div>

      {filteredStreams.length > 0 ? (
        <div className="overflow-x-auto bg-gray-800 border border-gray-700 rounded-lg">
          <Table>
            <TableHeader>
              <TableRow className="border-gray-700 hover:bg-gray-700/30">
                <TableHead className="w-[80px] hidden sm:table-cell text-gray-300">{t('table.thumbnail')}</TableHead>
                <TableHead className="text-gray-300">{t('table.title')}</TableHead>
                <TableHead className="text-gray-300">{t('table.status')}</TableHead>
                <TableHead className="text-right text-gray-300">{t('table.views')}</TableHead>
                <TableHead className="text-right hidden md:table-cell text-gray-300">{t('table.createdDate')}</TableHead>
                <TableHead className="text-right text-gray-300"><span className="sr-only">{t('table.actions')}</span></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredStreams.map((stream) => (
                <TableRow key={stream.id} className="border-gray-700 hover:bg-gray-700/30">
                  <TableCell className="hidden sm:table-cell">
                    {stream.thumbnail_url ? (
                        <Image src={stream.thumbnail_url} alt={stream.title} width={64} height={40} className="h-10 w-16 object-cover rounded-sm" />
                    ) : (
                        <div className="h-10 w-16 bg-gray-700 flex items-center justify-center rounded-sm">
                            <Video className="h-6 w-6 text-gray-500" />
                        </div>
                    )}
                  </TableCell>
                  <TableCell className="font-medium text-white">{stream.title}</TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(stream.status, stream.is_actually_live)} className="capitalize">
                      {getStatusDisplayText(stream.status, stream.is_actually_live)}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right text-gray-300">{stream.views?.toLocaleString() || '-'}</TableCell>
                  <TableCell className="text-right hidden md:table-cell text-gray-300">{formatDate(stream.created_at, locale)}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" className="border-gray-600 hover:bg-gray-700 text-gray-300">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-gray-800 border-gray-700 text-white">

                        <DropdownMenuItem 
                          className="focus:bg-red-600 text-red-400 focus:text-white cursor-pointer"
                          onClick={() => setDeleteStreamId(stream.id)}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          {t('actions.delete')}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ) : (
        <div className="text-center py-12 bg-gray-800 border border-gray-700 rounded-lg">
          <Video className="mx-auto h-16 w-16 text-gray-500 mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">{t('noStreams.title')}</h3>
          <p className="text-gray-400 mb-6">{t('noStreams.description')}</p>

        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!deleteStreamId} onOpenChange={() => setDeleteStreamId(null)}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white">
          <DialogHeader>
            <DialogTitle>{t('actions.confirmDelete')}</DialogTitle>
            <DialogDescription className="text-gray-400">
              {t('actions.deleteWarning')}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteStreamId(null)}
              disabled={isDeleting}
              className="border-gray-600 hover:bg-gray-700"
            >
              {t('actions.cancel')}
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteStream}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? t('actions.deleting') : t('actions.deleteStream')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 