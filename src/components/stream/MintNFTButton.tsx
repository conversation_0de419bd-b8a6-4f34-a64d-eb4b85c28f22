'use client';

import { useState, useCallback } from 'react';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { WalletButton } from '@/components/wallet/WalletButton';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Transaction, Keypair, SystemProgram, PublicKey } from '@solana/web3.js';
import { 
  TOKEN_PROGRAM_ID,
  createInitializeMintInstruction,
  createMintToInstruction,
  createAssociatedTokenAccountInstruction,
  getAssociatedTokenAddressSync,
  MINT_SIZE,
} from '@solana/spl-token';
import { createCreateMetadataAccountV3Instruction, createCreateMasterEditionV3Instruction } from '@metaplex-foundation/mpl-token-metadata';
import { AlertCircle, CheckCircle, Loader2, Coins } from 'lucide-react';
import { useParams } from 'next/navigation';
import { captureVideoFrame } from '@/lib/video-capture';
import { useToast } from '@/components/ui/toast';
import Image from 'next/image';

type MintNFTButtonProps = {
  streamId: string;
  currentTime: number;
  creatorWalletAddress: string;
  creatorName?: string;
  className?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  videoElement?: HTMLVideoElement; // Add video element for frame capture
  onSuccess?: () => void; // Add success callback
};

type MintStatus = 'idle' | 'preparing' | 'capturing' | 'signing' | 'confirming' | 'recording' | 'success' | 'error';

// BONK cost for NFT minting (200,000 BONK = ~$4.62 USD)(2 BONK for testing)
const NFT_MOMENT_COST_BONK = 2;

export function MintNFTButton({ 
  streamId, 
  currentTime,
  creatorWalletAddress, 
  creatorName,
  className = '',
  videoElement,
  onSuccess,
}: MintNFTButtonProps) {
  const { connected, publicKey, signTransaction, signMessage } = useWallet();
  const { connection } = useConnection();
  const params = useParams();
  const locale = params.locale as string || 'en';
  const { toast } = useToast();
  
  const [isOpen, setIsOpen] = useState(false);
  const [status, setStatus] = useState<MintStatus>('idle');
  const [error, setError] = useState<string | null>(null);
  const [explorerUrl, setExplorerUrl] = useState<string | null>(null);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);

  // Handle mint button click
  const handleOpenMintModal = async () => {
    if (!connected) return;
    setIsOpen(true);
    setStatus('idle');
    setError(null);
    setExplorerUrl(null);
    setCapturedImage(null);
    
    // Immediately capture and preview the current frame when opening modal
    if (videoElement) {
      try {
        const previewFrame = await captureVideoFrame(videoElement);
        if (previewFrame && previewFrame.length > 0) {
          setCapturedImage(previewFrame);
        }
      } catch (error) {
        console.warn('Failed to capture preview frame:', error);
      }
    }
  };

  // Process the BONK moment capture
  const handleCaptureMoment = useCallback(async () => {
    if (!connected || !publicKey) {
      toast({
        description: 'Please connect your wallet first',
        variant: 'destructive'
      });
      return;
    }
    
    try {
      setStatus('preparing');
      setError(null);

      // Step 1: Capture video frame (use preview if available, otherwise capture fresh)
      setStatus('capturing');
      let imageUrl = '';
      
      if (videoElement) {
        try {
          // Use captured preview if available, otherwise capture fresh
          const capturedFrame = capturedImage || await captureVideoFrame(videoElement);
          if (capturedFrame && capturedFrame.length > 0) {
            imageUrl = capturedFrame;
          } else {
            throw new Error('Video capture returned empty or invalid data');
          }
        } catch (captureError) {
          console.error('Failed to capture video frame:', captureError);
          const errorMessage = captureError instanceof Error ? captureError.message : 'Unknown error during capture';
          setError(`Failed to capture video frame: ${errorMessage}`);
          return;
        }
      } else {
        setError('No video element available for frame capture');
        return;
      }

      // Validate we have a proper image
      if (!imageUrl || imageUrl.trim() === '') {
        setError('No valid image captured for NFT creation');
        return;
      }

      // Step 2: Create wallet signature for security
      setStatus('preparing');
      const message = `Mint NFT for stream ${streamId} at ${currentTime}s - ${Date.now()}`;
      let walletSignature = '';
      
      try {
        const messageBytes = new TextEncoder().encode(message);
        const signature = await signMessage!(messageBytes);
        walletSignature = Buffer.from(signature).toString('base64');
      } catch (signError) {
        console.error('Failed to sign message:', signError);
        throw new Error('Failed to authenticate wallet. Please try again.');
      }

      // Step 3: Create BONK payment and NFT instructions
      const response = await fetch(`/${locale}/api/mint-moment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          streamId,
          timestamp: currentTime,
          imageUrl,
          creatorWalletAddress,
          creatorName,
          userWalletAddress: publicKey.toString(),
          walletSignature,
          walletMessage: message,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        
        // Handle specific IPFS upload errors
        if (errorData.error?.includes('IPFS service API keys')) {
          throw new Error('Image upload failed: IPFS service not configured. Please contact support.');
        }
        
        throw new Error(errorData.error || 'Failed to create transactions');
      }

      const data = await response.json();

      // Check if client-side NFT creation is needed
      if (data.clientSideNft) {
        
        // Step 4a: Sign and send BONK payment transaction with fresh blockhash
        setStatus('signing');
        const bonkTransaction = Transaction.from(Buffer.from(data.bonkTransaction, 'base64'));
        
        // Get fresh blockhash to avoid expiration
        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('confirmed');
        bonkTransaction.recentBlockhash = blockhash;
        bonkTransaction.lastValidBlockHeight = lastValidBlockHeight;
        
        const signedBonkTransaction = await signTransaction!(bonkTransaction);

        setStatus('confirming');
        let bonkSignature: string;
        try {
          bonkSignature = await connection.sendRawTransaction(signedBonkTransaction.serialize(), {
            skipPreflight: false,
            preflightCommitment: 'confirmed'
          });
          
          // Use block height confirmation for better reliability
          await connection.confirmTransaction({
            signature: bonkSignature,
            blockhash,
            lastValidBlockHeight
          }, 'confirmed');
          
        } catch (txError: unknown) {
          console.error('BONK transaction failed:', txError);
          const errorMessage = txError instanceof Error ? txError.message : String(txError);
          if (errorMessage.includes('Blockhash not found')) {
            throw new Error('Transaction expired. Please try again.');
          }
          throw new Error(`BONK payment failed: ${errorMessage}`);
        }

        // Step 4b: Create PROPER NFT with Master Edition (3 transactions for proper collectible)
        setStatus('signing');
        
        // Generate mint keypair for the NFT
        const mintKeypair = Keypair.generate();
        const mint = mintKeypair.publicKey;
        const associatedTokenAccount = getAssociatedTokenAddressSync(mint, publicKey);
        
        // Declare variables for later use
        let finalUri = data.metadataUri;
        
        // Calculate rent for mint account
        const mintRent = await connection.getMinimumBalanceForRentExemption(MINT_SIZE);
        
        // Transaction 1: Create mint account, token account, and mint 1 NFT
        const tx1 = new Transaction();
        
        // Create mint account
        tx1.add(
          SystemProgram.createAccount({
            fromPubkey: publicKey,
            newAccountPubkey: mint,
            space: MINT_SIZE,
            lamports: mintRent,
            programId: TOKEN_PROGRAM_ID,
          })
        );
        
        // Initialize mint with 0 decimals (NFT requirement)
        tx1.add(
          createInitializeMintInstruction(
            mint,
            0, // 0 decimals = NFT
            publicKey, // mint authority (will be transferred to Master Edition)
            publicKey, // freeze authority (required for Master Edition)
          )
        );
        
        // Create associated token account
        tx1.add(
          createAssociatedTokenAccountInstruction(
            publicKey, // payer
            associatedTokenAccount,
            publicKey, // owner
            mint
          )
        );
        
        // Mint exactly 1 token (NFT requirement)
        tx1.add(
          createMintToInstruction(
            mint,
            associatedTokenAccount,
            publicKey, // mint authority
            1 // amount: exactly 1 NFT
          )
        );
        
        // Set transaction properties with fresh blockhash
        const { blockhash: blockhash1, lastValidBlockHeight: lastValidBlockHeight1 } = await connection.getLatestBlockhash('confirmed');
        tx1.recentBlockhash = blockhash1;
        tx1.lastValidBlockHeight = lastValidBlockHeight1;
        tx1.feePayer = publicKey;
        
        // Sign with mint keypair
        tx1.partialSign(mintKeypair);
        
        // Sign and send transaction 1 with proper error handling
        try {
          const signedTx1 = await signTransaction!(tx1);
          const tx1Signature = await connection.sendRawTransaction(signedTx1.serialize(), {
            skipPreflight: false,
            preflightCommitment: 'confirmed'
          });
          
          await connection.confirmTransaction({
            signature: tx1Signature,
            blockhash: blockhash1,
            lastValidBlockHeight: lastValidBlockHeight1
          }, 'confirmed');
          
        } catch (tx1Error: unknown) {
          console.error('NFT creation transaction 1 failed:', tx1Error);
          const errorMessage = tx1Error instanceof Error ? tx1Error.message : String(tx1Error);
          if (errorMessage.includes('Blockhash not found')) {
            throw new Error('NFT creation expired. Please try again.');
          }
          throw new Error(`NFT creation failed: ${errorMessage}`);
        }
        
        // Transaction 2: Create metadata account
        const tx2 = new Transaction();
        
        // Calculate metadata PDA
        const TOKEN_METADATA_PROGRAM_ID = new PublicKey('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s');
        
        const [calculatedMetadataPda] = PublicKey.findProgramAddressSync(
          [
            Buffer.from('metadata'),
            TOKEN_METADATA_PROGRAM_ID.toBuffer(),
            mint.toBuffer(),
          ],
          TOKEN_METADATA_PROGRAM_ID
        );
        const metadataPda = calculatedMetadataPda;
        
        // HYBRID APPROACH: Minimal on-chain metadata + Rich IPFS metadata
        // This keeps transaction under 1232 bytes while preserving rich data in IPFS
        
        // Solana has strict URI length limits (~180 chars), truncate if needed
        if (finalUri && finalUri.length > 180) {
          console.warn('⚠️ URI too long for transaction, truncating to fit');
          // Truncate the URI to fit transaction limits
          finalUri = finalUri.substring(0, 180);
        }
        
        // Create ultra-minimal on-chain metadata to fit in transaction
        const metadataInstruction = createCreateMetadataAccountV3Instruction(
          {
            metadata: metadataPda,
            mint: mint,
            mintAuthority: publicKey,
            payer: publicKey,
            updateAuthority: publicKey,
          },
          {
            createMetadataAccountArgsV3: {
              data: {
                name: `Moment ${Date.now().toString().slice(-6)}`, // Ultra short name with timestamp
                symbol: 'BONK', // Short symbol
                uri: finalUri, // Use the transaction-safe URI
                sellerFeeBasisPoints: 0,
                creators: null, // Remove to save space
                collection: null,
                uses: null,
              },
              isMutable: true, // Allow updates to add rich metadata later
              collectionDetails: null,
            },
          }
        );
        
        tx2.add(metadataInstruction);
        
        // Set transaction properties with fresh blockhash
        const { blockhash: blockhash2, lastValidBlockHeight: lastValidBlockHeight2 } = await connection.getLatestBlockhash('confirmed');
        tx2.recentBlockhash = blockhash2;
        tx2.lastValidBlockHeight = lastValidBlockHeight2;
        tx2.feePayer = publicKey;
        
        // Sign and send transaction 2 with better error handling
        try {
          const signedTx2 = await signTransaction!(tx2);
          const tx2Signature = await connection.sendRawTransaction(signedTx2.serialize(), {
            skipPreflight: false,
            preflightCommitment: 'confirmed'
          });
          
          await connection.confirmTransaction({
            signature: tx2Signature,
            blockhash: blockhash2,
            lastValidBlockHeight: lastValidBlockHeight2
          }, 'confirmed');
          
        } catch (metadataError: unknown) {
          console.error('Metadata creation failed:', metadataError);
          const errorMessage = metadataError instanceof Error ? metadataError.message : String(metadataError);
          if (errorMessage.includes('Blockhash not found')) {
            throw new Error('Metadata creation expired. Please try again.');
          }
          if (errorMessage.includes('URI too long')) {
            throw new Error('Metadata URI too long. Please try again with a shorter image URL.');
          }
          throw new Error(`Failed to create NFT metadata: ${errorMessage}`);
        }
        
        // Transaction 3: Create Master Edition (THIS MAKES IT A REAL NFT!)
        const tx3 = new Transaction();
        
        // Calculate Master Edition PDA
        const [calculatedMasterEditionPda] = PublicKey.findProgramAddressSync(
          [
            Buffer.from('metadata'),
            TOKEN_METADATA_PROGRAM_ID.toBuffer(),
            mint.toBuffer(),
            Buffer.from('edition'),
          ],
          TOKEN_METADATA_PROGRAM_ID
        );
        const masterEditionPda = calculatedMasterEditionPda;
        
        // Create Master Edition instruction
        const masterEditionInstruction = createCreateMasterEditionV3Instruction(
          {
            edition: masterEditionPda,
            mint: mint,
            updateAuthority: publicKey,
            mintAuthority: publicKey,
            payer: publicKey,
            metadata: metadataPda,
          },
          {
            createMasterEditionArgs: {
              maxSupply: 0, // 0 = no prints allowed (unique NFT)
            },
          }
        );
        
        tx3.add(masterEditionInstruction);
        
        // After Master Edition creation, the mint authority is automatically transferred 
        // to the Master Edition PDA, making this a proper non-fungible token
        
        // Set transaction properties with fresh blockhash
        const { blockhash: blockhash3, lastValidBlockHeight: lastValidBlockHeight3 } = await connection.getLatestBlockhash('confirmed');
        tx3.recentBlockhash = blockhash3;
        tx3.lastValidBlockHeight = lastValidBlockHeight3;
        tx3.feePayer = publicKey;
        
        // Sign and send transaction 3 with proper error handling
        try {
          const signedTx3 = await signTransaction!(tx3);
          const tx3Signature = await connection.sendRawTransaction(signedTx3.serialize(), {
            skipPreflight: false,
            preflightCommitment: 'confirmed'
          });
          
          await connection.confirmTransaction({
            signature: tx3Signature,
            blockhash: blockhash3,
            lastValidBlockHeight: lastValidBlockHeight3
          }, 'confirmed');
          
        } catch (masterEditionError: unknown) {
          console.error('Master Edition creation failed:', masterEditionError);
          const errorMessage = masterEditionError instanceof Error ? masterEditionError.message : String(masterEditionError);
          if (errorMessage.includes('Blockhash not found')) {
            throw new Error('Master Edition creation expired. Please try again.');
          }
          throw new Error(`Failed to create Master Edition: ${errorMessage}`);
        }
        
        const nftSignature = mint.toString();

        // Step 5: Record the completed moment with both signatures and rich metadata URI
        setStatus('recording');
        const recordResponse = await fetch(`/${locale}/api/mint-moment/complete`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            streamId,
            timestamp: currentTime,
            imageUrl,
            creatorWalletAddress,
            userWalletAddress: publicKey.toString(),
            bonkTransactionSignature: bonkSignature,
            nftTransactionSignature: nftSignature,
            metadataUri: data.metadataUri, // Store the RICH metadata URI
            richMetadataUri: data.metadataUri, // Rich IPFS metadata with all collection data
            onChainMetadataUri: finalUri, // Minimal on-chain metadata URI
            mintAddress: nftSignature,
            metadata: data.metadata,
            mintCostBonk: data.mintCostBonk,
            platformFeePercentage: data.platformFeePercentage,
            creatorAmountBonk: data.creatorAmountBonk,
            platformFeeAmountBonk: data.platformFeeAmountBonk,
          }),
        });

        if (!recordResponse.ok) {
          const errorData = await recordResponse.json();
          console.error('Failed to record completed NFT:', errorData);
          throw new Error('NFT created but failed to record');
        }

        const recordData = await recordResponse.json();
        setExplorerUrl(recordData.nft?.bonkExplorerUrl);

        setStatus('success');
        onSuccess?.(); // Call success callback to refresh stream data
        toast({
          description: `🎉 Real NFT created in your wallet! Mint: ${nftSignature.substring(0, 8)}... Paid ${NFT_MOMENT_COST_BONK} BONK`,
          variant: 'success'
        });
      } else if (data.requiresTwoTransactions) {
        
        // Step 3a: Sign and send BONK payment transaction
        setStatus('signing');
        const bonkTransaction = Transaction.from(Buffer.from(data.bonkTransaction, 'base64'));
        const signedBonkTransaction = await signTransaction!(bonkTransaction);

        setStatus('confirming');
        const bonkSignature = await connection.sendRawTransaction(signedBonkTransaction.serialize());
        await connection.confirmTransaction(bonkSignature);

        // Step 3b: Sign and send NFT mint transaction
        setStatus('signing');
        const nftTransaction = Transaction.from(Buffer.from(data.nftTransaction, 'base64'));
        const signedNftTransaction = await signTransaction!(nftTransaction);

        setStatus('confirming');
        const nftSignature = await connection.sendRawTransaction(signedNftTransaction.serialize());
        await connection.confirmTransaction(nftSignature);

        // Step 4: Record the completed moment with both signatures
        setStatus('recording');
        const recordResponse = await fetch(`/${locale}/api/mint-moment/complete`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            streamId,
            timestamp: currentTime,
            imageUrl,
            creatorWalletAddress,
            userWalletAddress: publicKey.toString(),
            bonkTransactionSignature: bonkSignature,
            nftTransactionSignature: nftSignature,
            metadataUri: data.metadataUri,
            mintAddress: data.mintAddress, // Include the actual NFT mint address
            metadata: data.metadata,
            mintCostBonk: data.mintCostBonk,
            platformFeePercentage: data.platformFeePercentage,
            creatorAmountBonk: data.creatorAmountBonk,
            platformFeeAmountBonk: data.platformFeeAmountBonk,
          }),
        });

        if (!recordResponse.ok) {
          const errorData = await recordResponse.json();
          console.error('Failed to record completed NFT:', errorData);
          throw new Error('Transactions successful but failed to record NFT');
        }

        const recordData = await recordResponse.json();
        setExplorerUrl(recordData.nft?.bonkExplorerUrl);

        setStatus('success');
        onSuccess?.(); // Call success callback to refresh stream data
        toast({
          description: `🎉 Real NFT minted successfully! Check your wallet for mint: ${data.mintAddress?.substring(0, 8)}... Paid ${NFT_MOMENT_COST_BONK} BONK`,
          variant: 'success'
        });
      } else {
        // Legacy single transaction flow (BONK only)
        
        // Step 3: Sign and send BONK payment transaction
        setStatus('signing');
        const transaction = Transaction.from(Buffer.from(data.bonkTransaction, 'base64'));
        const signedTransaction = await signTransaction!(transaction);

        setStatus('confirming');
        const signature = await connection.sendRawTransaction(signedTransaction.serialize());
        await connection.confirmTransaction(signature);

        // Step 4: Record the moment capture (without NFT)
        setStatus('recording');
        const recordResponse = await fetch(`/${locale}/api/mint-moment/complete`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            streamId,
            timestamp: currentTime,
            imageUrl,
            creatorWalletAddress,
            userWalletAddress: publicKey.toString(),
            bonkTransactionSignature: signature,
            metadata: data.metadata,
            mintCostBonk: data.mintCostBonk,
            platformFeePercentage: data.platformFeePercentage,
            creatorAmountBonk: data.creatorAmountBonk,
            platformFeeAmountBonk: data.platformFeeAmountBonk,
          }),
        });

        if (!recordResponse.ok) {
          const errorData = await recordResponse.json();
          console.error('Failed to record moment:', errorData);
          throw new Error('Payment successful but failed to record moment');
        }

        const recordData = await recordResponse.json();
        setExplorerUrl(recordData.nft?.bonkExplorerUrl);

        setStatus('success');
        onSuccess?.(); // Call success callback to refresh stream data
        toast({
          description: `🎉 Real NFT Collectible Created! Check your wallet's "Collectibles" section! Paid ${NFT_MOMENT_COST_BONK} BONK to support ${creatorName || 'creator'}`,
          variant: 'success'
        });
      }
      
    } catch (error) {
      console.error('Capture error:', error);
      setStatus('error');
      setError(error instanceof Error ? error.message : 'Failed to capture moment');
      toast({
        description: 'Failed to capture moment',
        variant: 'destructive'
      });
    }
  }, [connected, publicKey, videoElement, streamId, currentTime, creatorWalletAddress, creatorName, locale, connection, signTransaction, signMessage, toast, capturedImage, onSuccess]);

  // Get status display info
  const getStatusInfo = () => {
    switch (status) {
      case 'idle':
        return { 
          icon: <Coins className="w-4 h-4" />, 
          text: 'Ready to mint NFT', 
          color: 'text-white' 
        };
      case 'preparing':
        return { 
          icon: <Loader2 className="w-4 h-4 animate-spin" />, 
          text: 'Preparing...', 
          color: 'text-blue-400' 
        };
      case 'capturing':
        return { 
          icon: <Loader2 className="w-4 h-4 animate-spin" />, 
          text: 'Capturing frame...', 
          color: 'text-blue-400' 
        };
      case 'signing':
        return { 
          icon: <Loader2 className="w-4 h-4 animate-spin" />, 
          text: 'Sign payment...', 
          color: 'text-yellow-400' 
        };
      case 'confirming':
        return { 
          icon: <Loader2 className="w-4 h-4 animate-spin" />, 
          text: 'Confirming payment...', 
          color: 'text-orange-400' 
        };
      case 'recording':
        return { 
          icon: <Loader2 className="w-4 h-4 animate-spin" />, 
          text: 'Creating NFT...', 
          color: 'text-green-400' 
        };
      case 'success':
        return { 
          icon: <CheckCircle className="w-4 h-4" />, 
          text: 'NFT Created!', 
          color: 'text-green-400' 
        };
      case 'error':
        return { 
          icon: <AlertCircle className="w-4 h-4" />, 
          text: 'Error occurred', 
          color: 'text-red-400' 
        };
      default:
        return { 
          icon: <Coins className="w-4 h-4" />, 
          text: 'Capture Moment', 
          color: 'text-white' 
        };
    }
  };

  const statusInfo = getStatusInfo();
  const isProcessing = ['preparing', 'capturing', 'signing', 'confirming', 'recording'].includes(status);

  if (!connected) {
    return <WalletButton />;
  }

  return (
    <>
      <Button
        onClick={handleOpenMintModal}
        disabled={isProcessing}
        className={className || `text-white border-0 font-bold`}
        style={{ backgroundColor: '#FF5C01', ...(className?.includes('style') ? {} : { border: 'none' }) }}
        onMouseEnter={(e) => {
          if (!className?.includes('hover:')) {
            e.currentTarget.style.backgroundColor = '#e5530a';
          }
        }}
        onMouseLeave={(e) => {
          if (!className?.includes('hover:')) {
            e.currentTarget.style.backgroundColor = '#FF5C01';
          }
        }}
      >
        <Coins className="w-4 h-4 mr-2" />
        MINT NFT
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-md !top-[10%] !translate-y-0">
          <DialogHeader>
            <DialogTitle className="text-white">Mint Real NFT Collectible</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            
            {/* Image Preview */}
            {capturedImage && (
              <div className="p-3 bg-gray-800 rounded-lg">
                <p className="text-sm text-gray-300 mb-2">NFT Preview</p>
                <div className="relative w-full h-48 bg-gray-700 rounded-lg overflow-hidden">
                  <Image 
                    src={capturedImage} 
                    alt="Captured moment preview" 
                    fill
                    className="object-cover"
                    unoptimized // Since it's a data URL from video capture
                  />
                  <div className="absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                    {Math.floor(currentTime / 60)}:{(currentTime % 60).toFixed(0).padStart(2, '0')}
                  </div>
                </div>
              </div>
            )}
            
            {creatorName && (
              <div className="flex items-center space-x-3 p-3 bg-gray-800 rounded-lg">
                <div className="w-8 h-8 bg-[#DDB77A] rounded-full flex items-center justify-center">
                  <span className="text-gray-900 font-bold text-sm">
                    {creatorName.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <p className="text-sm text-gray-300">Creator</p>
                  <p className="text-white font-medium">{creatorName}</p>
                </div>
              </div>
            )}

            <div className="flex items-center space-x-3 p-3 bg-orange-900/20 border border-orange-500/20 rounded-lg">
              <Coins className="w-5 h-5 text-orange-400" />
              <div>
                <p className="text-sm text-gray-300">Payment</p>
                <p className="text-orange-400 font-medium">{NFT_MOMENT_COST_BONK} BONK</p>
              </div>
            </div>

            {/* Status display */}
            <div className="flex items-center space-x-3 p-3 bg-gray-800 rounded-lg">
              <span className={statusInfo.color}>{statusInfo.icon}</span>
              <div>
                <p className="text-sm text-gray-300">Status</p>
                <p className={`font-medium ${statusInfo.color}`}>{statusInfo.text}</p>
              </div>
            </div>

            {error && (
              <div className="p-3 bg-red-900/20 border border-red-500/20 rounded-lg">
                <div className="flex items-center space-x-2">
                  <AlertCircle className="w-4 h-4 text-red-400" />
                  <p className="text-red-400 text-sm">{error}</p>
                </div>
              </div>
            )}

            {status === 'success' && explorerUrl && (
              <div className="p-3 bg-green-900/20 border border-green-500/20 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-green-400 text-sm">Payment confirmed!</span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(explorerUrl, '_blank')}
                    className="border-green-600 text-green-400 hover:bg-green-900/30"
                  >
                    View Transaction
                  </Button>
                </div>
              </div>
            )}

            <div className="text-sm text-gray-400 space-y-1">
              <p>• {(NFT_MOMENT_COST_BONK * 0.75).toFixed(1)} BONK to creator (75%)</p>
              <p>• {(NFT_MOMENT_COST_BONK * 0.25).toFixed(1)} BONK platform fee (25%)</p>
              <p>• Moment captured and minted as real NFT collectible</p>
            </div>
          </div>

          <DialogFooter className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isProcessing}
              className="border-gray-600 text-gray-300 hover:bg-gray-800"
            >
              Cancel
            </Button>
            <Button
              onClick={handleCaptureMoment}
              disabled={isProcessing || status === 'success'}
              className="bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600 text-white"
            >
              {status === 'success' ? (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  NFT Minted!
                </>
              ) : isProcessing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {statusInfo.text}
                </>
              ) : (
                <>
                  <Coins className="w-4 h-4 mr-2" />
                  Pay {NFT_MOMENT_COST_BONK} BONK
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
