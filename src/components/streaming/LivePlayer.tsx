'use client';

import React, { useState, useEffect, useCallback, memo } from 'react';
import { PlayCircle, PauseCircle, Volume2, VolumeX, Maximize, RefreshCw } from 'lucide-react';
import * as Player from '@livepeer/react/player';
import { useParams } from 'next/navigation';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

type LivePlayerProps = {
  streamId?: string;
  playbackId: string;
  isLive?: boolean;
  title: string;
  onPlay?: () => void;
  onPause?: () => void;
  aspectRatio?: number;
  controls?: boolean;
  autoPlay?: boolean;
  onVideoRef?: (videoElement: HTMLVideoElement | null) => void; // Add callback for video element
};

export const LivePlayer = memo(function LivePlayer({
  playbackId,
  isLive = true,
  title,
  onPlay,
  onPause,
  aspectRatio = 16/9,
  controls = true,
  autoPlay = true,
  onVideoRef
}: LivePlayerProps) {
  const params = useParams();
  const locale = params.locale as string || 'en';
  const [src, setSrc] = useState<unknown>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [livepeerReady, setLivepeerReady] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [streamStatus, setStreamStatus] = useState<'checking' | 'live' | 'offline' | 'error' | 'retrying'>('checking');
  const maxRetries = 3;

  // Retry function for transient errors - separate from main loading logic
  const retryPlayback = useCallback(async () => {
    if (retryCount >= maxRetries) {
      setError("Stream failed to load after multiple attempts. Please try refreshing the page.");
      setStreamStatus('error');
      return;
    }

    setRetryCount(prev => prev + 1);
    setError(null);
    setIsLoading(true);
    setStreamStatus('retrying');

    try {
      // Always set a fallback direct HLS URL for immediate playback
      const fallbackSrc = [{
        type: 'hls',
        mime: 'application/vnd.apple.mpegurl',
        src: `https://livepeercdn.studio/hls/${playbackId}/index.m3u8`
      }];
      
      setSrc(fallbackSrc);
      setLivepeerReady(true);
      setStreamStatus('live');
    } catch (err) {
      console.error('Error during retry:', err);
      setError('Retry failed');
      setStreamStatus('error');
    } finally {
      setIsLoading(false);
    }
  }, [retryCount, maxRetries, playbackId]);

  // Main effect to load the stream - only depends on playbackId
  useEffect(() => {
    // Early validation of required props
    if (!playbackId || playbackId.trim() === '') {
      setError('No playback ID provided');
      setStreamStatus('error');
      setIsLoading(false);
      return;
    }

    
    const getPlaybackSource = async () => {
      try {
        setIsLoading(true);
        setError(null);
        setStreamStatus('checking');

        // Always set a fallback direct HLS URL first for immediate playback
        const fallbackSrc = [{
          type: 'hls',
          mime: 'application/vnd.apple.mpegurl',
          src: `https://livepeercdn.studio/hls/${playbackId}/index.m3u8`
        }];
        
        setSrc(fallbackSrc);
        setLivepeerReady(true);
        setStreamStatus('live');
        
        // Optionally try to get optimized playback info from our API - only if playbackId is valid
        if (playbackId && playbackId.length > 10) {
          try {
            const response = await fetch(`/${locale}/api/stream-playback?playbackId=${playbackId}`);
            if (response.ok) {
              const data = await response.json();
              
              if (data.src && data.src.length > 0) {
                setSrc(data.src);
              }
            } else {
              console.warn(`API returned ${response.status}, using fallback source`);
            }
          } catch (apiError) {
            console.warn('API request failed, using fallback source:', apiError);
            // Continue with fallback source
          }
        }
      } catch (err) {
        console.error('Error getting playback source:', err);
        setError('Failed to load stream');
        setStreamStatus('error');
      } finally {
        setIsLoading(false);
      }
    };

    if (playbackId) {
      // Reset retry count when playbackId changes
      setRetryCount(0);
      getPlaybackSource();
    }
  }, [playbackId, locale]); // Only depend on playbackId and locale

  const handleRetry = useCallback(async () => {
    await retryPlayback();
  }, [retryPlayback]);

  // Early validation of required props - now after hooks
  if (!playbackId || playbackId.trim() === '') {
    return (
      <div className="w-full h-full flex items-center justify-center bg-black">
        <div className="text-center text-white">
          <p className="text-red-500 mb-2">Invalid stream configuration</p>
          <p className="text-gray-400 text-sm">No playback ID provided</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-black">
        <LoadingSpinner size="md" variant="simple" />
      </div>
    );
  }

  if (error || streamStatus === 'offline') {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-900 to-black">
        <div className="text-center text-white p-8 max-w-md">
          {streamStatus === 'offline' ? (
            <>
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-800 flex items-center justify-center">
                <span className="text-2xl">📺</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Stream Has Ended</h3>
              <p className="text-gray-400 text-sm mb-6">
                This live stream is no longer broadcasting. Check back later for new streams from this creator.
              </p>
              <button 
                className="px-6 py-3 bg-gradient-to-r from-[#FFD700] to-[#FF6B35] text-black rounded-full font-semibold hover:from-[#E6C300] hover:to-[#E55A2B] transition-all duration-200 flex items-center gap-2 mx-auto"
                onClick={handleRetry}
              >
                <RefreshCw size={16} />
                Check Again
              </button>
            </>
          ) : (
            <>
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-900 flex items-center justify-center">
                <span className="text-2xl">⚠️</span>
              </div>
              <p className="text-red-400 mb-2 font-semibold">
                Stream Error
              </p>
              <p className="text-gray-400 text-sm mb-4">
                {error}
              </p>
              {retryCount > 0 && (
                <p className="text-gray-400 text-sm mb-2">
                  Retry attempt: {retryCount}/{maxRetries}
                </p>
              )}
              <button 
                className="mt-4 px-4 py-2 bg-[#FFD700] text-black rounded-md flex items-center gap-2 mx-auto hover:bg-[#FFD700]/80 transition-colors"
                onClick={handleRetry}
                disabled={retryCount >= maxRetries && streamStatus === 'error'}
              >
                <RefreshCw size={16} />
                {retryCount >= maxRetries && streamStatus === 'error' ? 'Max Retries Reached' : 'Retry'}
              </button>
            </>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`relative w-full ${aspectRatio === 9/16 ? "aspect-[9/16]" : "aspect-video"} bg-black`}>
      {livepeerReady && src ? (
        <Player.Root
          src={src as unknown as Parameters<typeof Player.Root>[0]['src']}
          autoPlay={autoPlay}
          aspectRatio={aspectRatio}
          onError={(error) => {
            console.error("Livepeer player error for playbackId:", playbackId, "Error:", error);
            console.error("Source:", src);
            console.error("Error type:", typeof error);
            console.error("Retry count:", retryCount);
            
            // Completely ignore null/undefined errors - they are very common in Livepeer and usually transient
            if (error === null || error === undefined) {
              return; // Don't do anything for null errors
            }
            
            // Check if this is a "stream ended" type error
            const errorString = typeof error === 'string' ? error : error?.message || String(error);
            const streamEndedIndicators = [
              'stream open failed',
              'no levels found',
              'manifest parsing error',
              'stream not found',
              'hls: stream not available'
            ];
            
            const isStreamEndedError = streamEndedIndicators.some(indicator => 
              errorString.toLowerCase().includes(indicator.toLowerCase())
            );
            
            if (isStreamEndedError) {
              setStreamStatus('offline');
              setError(null); // Clear the technical error message
              return;
            }
            
            // Check for B-frames WebRTC error - auto-retry as it's often transient
            if (errorString.toLowerCase().includes('metadata indicates that webrtc playback contains bframes') || 
                errorString.toLowerCase().includes('bframes')) {
              
              // Only retry if we haven't exceeded retry limit
              if (retryCount < maxRetries) {
                setTimeout(() => {
                  retryPlayback();
                }, 2000); // Wait 2 seconds before retry
                return;
              } else {
                setError("WebRTC playback error. Please try refreshing the page or use a different browser.");
                setStreamStatus('error');
                return;
              }
            }
            
            // Only retry for serious network errors, and only if we haven't retried too much
            if (typeof error === 'string' && 
                (error as string).toLowerCase().includes('network') && 
                retryCount < 2) {
              setTimeout(() => {
                retryPlayback();
              }, 3000); // Wait 3 seconds before retry
              return;
            }
            
            // For other errors, provide specific messages but don't auto-retry
            let errorMessage = "Stream playback failed";
            if (typeof error === 'string') {
              errorMessage = `Stream error: ${error}`;
            } else if (error?.message) {
              errorMessage = `Stream error: ${error.message}`;
            }
            
            setError(errorMessage);
            setStreamStatus('error');
          }}
        >
          <Player.Container className="h-full w-full">
            <Player.Video 
              title={title} 
              className="h-full w-full object-cover" 
              ref={(videoElement: HTMLVideoElement | null) => {
                onVideoRef?.(videoElement);
              }}
              onPlay={() => {
                // Reset retry count on successful play
                setRetryCount(0);
                setError(null);
                onPlay?.();
              }}
              onPause={() => {
                onPause?.();
              }}
              onLoadStart={() => {
              }}
              onCanPlay={() => {
              }}
              onError={(e) => {
                console.error('Video element error for playbackId:', playbackId, e);
              }}
            />
            
            {isLive && streamStatus === 'live' && (
              <div className="absolute top-4 left-4 bg-red-600 text-white px-2 py-1 text-xs font-semibold rounded flex items-center gap-1">
                <span className="w-2 h-2 bg-white rounded-full animate-pulse"></span>
                LIVE
              </div>
            )}
            
            {controls && (
              <Player.Controls className="flex items-center justify-between p-4 absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent">
                <div className="flex items-center space-x-4">
                  <Player.PlayPauseTrigger className="w-10 h-10 text-white hover:text-[#FFD700] transition">
                    <Player.PlayingIndicator asChild matcher={false}>
                      <PlayCircle size={24} />
                    </Player.PlayingIndicator>
                    <Player.PlayingIndicator asChild>
                      <PauseCircle size={24} />
                    </Player.PlayingIndicator>
                  </Player.PlayPauseTrigger>
                  
                  <Player.MuteTrigger className="text-white hover:text-[#FFD700] transition">
                    <Player.VolumeIndicator asChild matcher={false}>
                      <VolumeX size={20} />
                    </Player.VolumeIndicator>
                    <Player.VolumeIndicator asChild>
                      <Volume2 size={20} />
                    </Player.VolumeIndicator>
                  </Player.MuteTrigger>
                  
                  <Player.Volume className="w-20 h-1 bg-white/30 rounded-full" />
                </div>
                
                <div className="flex items-center space-x-4">
                  {!isLive && (
                    <div className="text-white text-sm flex items-center">
                      <Player.Time className="w-12 text-right" />
                    </div>
                  )}
                  
                  <Player.FullscreenTrigger className="text-white hover:text-[#FFD700] transition">
                    <Maximize size={20} />
                  </Player.FullscreenTrigger>
                </div>
              </Player.Controls>
            )}
            
            <Player.ErrorIndicator asChild matcher="all">
              <div className="absolute inset-0 flex items-center justify-center bg-black/70 z-10">
                <div className="text-center p-4">
                  <p className="text-red-500 mb-4">Stream playback error</p>
                  <button 
                    className="px-4 py-2 bg-[#FFD700] text-black rounded-md flex items-center gap-2"
                    onClick={handleRetry}
                  >
                    <RefreshCw size={16} />
                    Retry
                  </button>
                </div>
              </div>
            </Player.ErrorIndicator>
          </Player.Container>
        </Player.Root>
      ) : (
        <div className="w-full h-full flex items-center justify-center bg-black">
          <LoadingSpinner size="md" variant="simple" />
        </div>
      )}
    </div>
  );
});
