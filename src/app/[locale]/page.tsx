'use client';

import { useTranslations } from 'next-intl';
import { Link } from '@/lib/i18n/index';
import Image from 'next/image';
import { useState, useEffect, useRef, memo, useMemo } from 'react';
// Removed Button import - using native HTML buttons to avoid hydration issues
import BonkPriceCard from '@/components/ui/bonk-price-card';
import { Play, Users, Zap, Shield, TrendingUp, Coins, Camera, Wallet, Link as LinkIcon, UserX, BarChart3, Bolt, Flame, Tv } from 'lucide-react';

// Simple intersection observer hook
const useSimpleInView = (ref: React.RefObject<HTMLElement | null>) => {
  const [isInView, setIsInView] = useState(false);

  useEffect(() => {
    if (!ref.current) return;

    const element = ref.current;
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { rootMargin: '-50px' }
    );

    observer.observe(element);
    return () => observer.disconnect();
  }, [ref]);

  return isInView;
};

// Helper function for safer translations
function useTranslationsWithFallback(namespace: string) {
  try {
    return useTranslations(namespace);
  } catch (error) {
    console.warn('Translation error:', error);
    return (key: string) => key;
  }
}



// Type definitions
interface Feature {
  icon: React.ComponentType<{ className?: string }>;
  titleKey: string;
  descKey: string;
  bgColor: string;
  fallbackTitle: string;
  fallbackDesc: string;
}

interface Step {
  step: string;
  icon: React.ComponentType<{ className?: string }>;
  titleKey: string;
  descKey: string;
  fallbackTitle: string;
  fallbackDesc: string;
}



interface FeatureCardProps {
  feature: Feature;
  index: number;
  t: (key: string) => string;
}

interface StepCardProps {
  step: Step;
  index: number;
  t: (key: string) => string;
}



// Static constant data to prevent re-renders and ensure SSR consistency
const FEATURES = [
  {
    icon: Tv,
    titleKey: 'feature_1_title',
    descKey: 'feature_1_desc',
    bgColor: '#FF5C01',
    fallbackTitle: 'Live Streaming',
    fallbackDesc: 'Stream live content in HD quality with real-time chat and engagement.'
  },
  {
    icon: Coins,
    titleKey: 'feature_2_title',
    descKey: 'feature_2_desc',
    bgColor: '#2B3849',
    fallbackTitle: 'Direct Creator Monetization',
    fallbackDesc: 'Earn BONK instantly while streaming - transparent 5% fee, maximum creator profits.'
  },
  {
    icon: Camera,
    titleKey: 'feature_3_title',
    descKey: 'feature_3_desc',
    bgColor: '#FFD302',
    fallbackTitle: 'NFT Moments',
    fallbackDesc: 'Capture epic frames from livestreams and save them to the blockchain forever.'
  }
] as const;

const STEPS = [
  {
    step: '01',
    icon: Wallet,
    titleKey: 'step_1_title',
    descKey: 'step_1_desc',
    fallbackTitle: 'Connect your wallet',
    fallbackDesc: 'Link your Phantom or Backpack wallet securely'
  },
  {
    step: '02',
    icon: Play,
    titleKey: 'step_2_title',
    descKey: 'step_2_desc',
    fallbackTitle: 'Watch streams',
    fallbackDesc: 'Discover and enjoy crypto-focused content'
  },
  {
    step: '03',
    icon: Coins,
    titleKey: 'step_3_title',
    descKey: 'step_3_desc',
    fallbackTitle: 'Tip with BONK',
    fallbackDesc: 'Support creators directly with BONK'
  },
  {
    step: '04',
    icon: Camera,
    titleKey: 'step_4_title',
    descKey: 'step_4_desc',
    fallbackTitle: 'Mint moments',
    fallbackDesc: 'Create NFTs from your favorite stream moments'
  }
] as const;



// Memoized components for better performance
const FeatureCard = memo(({ feature, t }: FeatureCardProps) => (
  <div className="text-center group h-full">
    <div
      className={`rounded-xl p-8 transition-all duration-300 group-hover:scale-105 h-full flex flex-col ${feature.bgColor === 'transparent' ? 'border-2 border-white hover:bg-white/20' : 'border border-white/10 shadow-bonk-xl hover:shadow-bonk-2xl'}`}
      style={{ backgroundColor: feature.bgColor }}
    >
      <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-white/20 p-1 group-hover:scale-110 transition-all duration-300 flex items-center justify-center">
        <feature.icon className="w-8 h-8 text-white group-hover:scale-110 transition-transform duration-300" />
      </div>
      <h3 className="bonk-header text-2xl md:text-3xl lg:text-4xl mb-4 transition-colors duration-300 text-white font-black">
        {t(feature.titleKey)}
      </h3>
      <p className="bonk-body text-base md:text-lg leading-relaxed group-hover:opacity-90 transition-opacity duration-300 text-white/80 flex-grow">
        {t(feature.descKey)}
      </p>
    </div>
  </div>
));

FeatureCard.displayName = 'FeatureCard';

const StepCard = memo(({ step, index, t }: StepCardProps) => (
  <div className="relative text-center group h-full">
    <div className="bg-transparent rounded-xl p-8 hover:shadow-bonk-xl transition-all duration-300 group-hover:scale-105 border border-white hover:bg-white/20 h-full flex flex-col">
      <div className="text-6xl bonk-header text-bonk-orange mb-4 group-hover:text-bonk-red transition-colors duration-300 font-bold">
        {step.step}
      </div>

      <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-bonk-orange p-1 group-hover:scale-110 transition-all duration-300 shadow-lg flex items-center justify-center group-hover:bg-bonk-red">
        <step.icon className="w-8 h-8 text-white group-hover:scale-110 transition-transform duration-300" />
      </div>

      <h3 className="bonk-header text-lg mb-3 text-white font-bold group-hover:text-bonk-orange transition-colors duration-300">
        {t(step.titleKey)}
      </h3>
      <p className="bonk-body text-white/80 text-sm leading-relaxed group-hover:text-white transition-colors duration-300 flex-grow">
        {t(step.descKey)}
      </p>
    </div>

    {index < 3 && (
      <div className="hidden lg:block absolute top-1/2 -right-4 w-8 h-0.5 bg-bonk-gradient-orange opacity-60 group-hover:opacity-100 transition-opacity duration-300 shadow-sm"></div>
    )}
  </div>
));

StepCard.displayName = 'StepCard';

// Helper component to render text with bold formatting
const FormattedText = memo(({ text }: { text: string }) => {
  const parts = text.split(/(\*\*[^*]+\*\*)/g);

  return (
    <>
      {parts.map((part, index) => {
        if (part.startsWith('**') && part.endsWith('**')) {
          return (
            <span key={index} className="font-bold text-bonk-yellow">
              {part.slice(2, -2)}
            </span>
          );
        }
        return <span key={index}>{part}</span>;
      })}
    </>
  );
});

FormattedText.displayName = 'FormattedText';



export default function HomePage() {


  // Intersection observer refs with proper typing
  const whyRef = useRef<HTMLElement | null>(null);
  const howRef = useRef<HTMLElement | null>(null);
  const streamsRef = useRef<HTMLElement | null>(null);
  const ctaRef = useRef<HTMLElement | null>(null);
  const aboutRef = useRef<HTMLElement | null>(null);

  // Optimized InView hooks using native intersection observer
  const whyInView = useSimpleInView(whyRef);
  const howInView = useSimpleInView(howRef);
  const streamsInView = useSimpleInView(streamsRef);
  const ctaInView = useSimpleInView(ctaRef);
  const aboutInView = useSimpleInView(aboutRef);

  // Memoized translations
  const t = useTranslationsWithFallback('common');
  
  // Pre-computed text content - always use translations to avoid hydration mismatch
  const textContent = useMemo(() => ({
    heroDescription: t('hero_description'),
    startWatching: t('start_watching'),
    becomeCreator: t('profile.become_creator'),
    platformFeatures: t('platform_features'),
    whyBonkstream: t('why_bonkstream'),
    howItWorks: t('how_it_works'),
    features: {
      transparent_payments: t('features.transparent_payments'),
      transparent_payments_desc: t('features.transparent_payments_desc'),
      direct_monetization: t('features.direct_monetization'),
      direct_monetization_desc: t('features.direct_monetization_desc'),
      no_ads: t('features.no_ads'),
      no_ads_desc: t('features.no_ads_desc'),
      no_moderation: t('features.no_moderation'),
      no_moderation_desc: t('features.no_moderation_desc'),
      anonymous_streaming: t('features.anonymous_streaming'),
      anonymous_streaming_desc: t('features.anonymous_streaming_desc'),
      no_manipulation: t('features.no_manipulation'),
      no_manipulation_desc: t('features.no_manipulation_desc'),
      instant_payouts: t('features.instant_payouts'),
      instant_payouts_desc: t('features.instant_payouts_desc'),
      fresh_content: t('features.fresh_content'),
      fresh_content_desc: t('features.fresh_content_desc')
    },
    appFeatures: {
      current: t('app_features.current'),
      coming_soon: t('app_features.coming_soon'),
      interactions: t('app_features.interactions')
    },
    cta: {
      joinRevolution: t('cta.join_revolution'),
      startStreaming: t('cta.start_streaming')
    },
    about: {
      title: t('about.title'),
      description: t('about.description'),
      holders: t('about.holders'),
      integrations: t('about.integrations'),
      chains: t('about.chains')
    }
  }), [t]);

  return (
    <div className="min-h-screen bg-bonk-gradient-bg text-white" style={{ transform: 'translateZ(0)' }}>
      {/* Hero Section */}
      <section className="relative h-screen flex flex-col">
        <div className="absolute inset-0 bg-bonk-gradient-sunset"></div>
        
        {/* Simplified floating elements */}
        <div className="absolute top-1/4 right-1/3 w-80 h-80 bg-bonk-gradient-radial rounded-full opacity-30 animate-pulse"></div>
        <div className="absolute bottom-32 left-1/4 w-16 h-16 bg-bonk-orange rounded-full opacity-60 animate-bounce"></div>
        
        <div className="flex-1 flex items-start justify-center relative z-30 container mx-auto px-4 text-center pt-20">
          <div className="w-full">
            {/* SBC'25 Badge above tagline */}
            <div className="flex justify-center mb-3 animate-hero-badge">
              <div className="bg-gradient-to-r from-bonk-orange to-bonk-yellow text-black px-3 py-1 sm:px-4 sm:py-2 rounded-full font-herborn font-black text-xs sm:text-sm tracking-wider shadow-lg border-2 border-white/20">
                SBC&apos;25
              </div>
            </div>

            {/* Tagline */}
            <p className="text-lg md:text-xl font-helvetica font-medium mb-4 tracking-wider text-white animate-hero-tagline">
              Stream. BONK. Mint.
            </p>

            {/* Title */}
            <div className="mb-6">
              <h1 className="text-4xl sm:text-5xl md:text-8xl hero-title-mobile font-herborn font-black text-[#FF0000] leading-tight px-2 text-center animate-hero-title">
                BONK<span className="text-white">STREAM</span>
              </h1>
            </div>

            {/* Description */}
            <p className="text-xl md:text-2xl font-helvetica font-medium text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed animate-hero-description">
              {textContent.heroDescription}
            </p>

            {/* Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center relative z-30">
              <Link href="/streams">
                <button className="bonk-btn text-lg px-8 py-4 font-bold shadow-lg hover:shadow-xl transition-all duration-300 relative z-30 bg-bonk-orange hover:bg-bonk-red text-white rounded-lg inline-flex items-center justify-center h-14 animate-hero-button-1">
                  <Play className="w-5 h-5 mr-2" />
                  {textContent.startWatching}
                </button>
              </Link>
              <Link href="/creator/register">
                <button className="border-white text-white hover:bg-white/20 hover:text-white text-lg px-8 py-4 font-bold shadow-lg hover:shadow-xl transition-all duration-300 relative z-30 border-2 rounded-lg inline-flex items-center justify-center h-14 animate-hero-button-2">
                  <Zap className="w-5 h-5 mr-2" />
                  {textContent.becomeCreator}
                </button>
              </Link>
            </div>
          </div>
        </div>
        
        {/* Optimized BONK character image with jump animation - positioned at color change edge */}
        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 z-20 hidden md:block" style={{ top: '123px' }}>
          <div className="animate-jump-in">
            <Image
              src="/Peekaboo_1.png"
              alt="BONK Character"
              width={2100}
              height={2100}
              className="w-[600px] h-[600px] md:w-[900px] md:h-[900px] lg:w-[1200px] lg:h-[1200px] object-contain"
              priority
              quality={75}
              sizes="(max-width: 768px) 600px, (max-width: 1024px) 900px, 1200px"
            />
          </div>
        </div>

        {/* Mobile BONK character image - properly sized for mobile */}
        <div className="absolute left-1/2 transform -translate-x-1/2 z-20 md:hidden" style={{ bottom: '-100px' }}>
          <div className="animate-jump-in w-full flex justify-center">
            <Image
              src="/Peekaboo_1.png"
              alt="BONK Character"
              width={1600}
              height={1600}
              className="w-[160vw] h-[160vw] max-w-[800px] max-h-[800px] object-contain mx-auto"
              priority
              quality={75}
              sizes="(max-width: 640px) 160vw, 800px"
            />
          </div>
        </div>
      </section>

      {/* Why BONKSTREAM Section */}
      <section ref={whyRef} className="pt-40 pb-20 relative" style={{
        background: 'linear-gradient(135deg, #FC8E01 0%, #FFD302 100%)',
        backgroundImage: 'url(/WhiteOutline.png)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}>
        <div className="absolute inset-0 bg-gradient-to-br from-bonk-orange/80 to-bonk-yellow/80"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className={`text-center mb-16 transition-all duration-1000 ${whyInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <h2 className="bonk-header text-4xl md:text-6xl text-black mb-6">
              {textContent.whyBonkstream}
            </h2>
            <div className="w-24 h-1 bg-bonk-red mx-auto rounded-full shadow-lg"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-stretch">
            {FEATURES.map((feature, index) => (
              <div
                key={index}
                className={`h-full transition-all duration-1000 ${whyInView ? `opacity-100 translate-y-0` : 'opacity-0 translate-y-4'}`}
                style={{ animationDelay: `${index * 80}ms` }}
              >
                <FeatureCard feature={feature} index={index} t={t} />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section ref={howRef} className="py-20 relative" style={{
        background: 'linear-gradient(135deg, #2B3849 0%, #000205 100%)'
      }}>
        <div className="container mx-auto px-4 relative z-10">
          <div className={`text-center mb-16 transition-all duration-1000 ${howInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <h2 className="bonk-header text-4xl md:text-6xl text-white mb-6">
              {textContent.howItWorks}
            </h2>
            <div className="w-24 h-1 bg-bonk-gradient-orange mx-auto rounded-full shadow-lg"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 items-stretch">
            {STEPS.map((step, index) => (
              <div
                key={index}
                className={`h-full transition-all duration-1000 ${howInView ? `opacity-100 translate-y-0` : 'opacity-0 translate-y-4'}`}
                style={{ animationDelay: `${index * 80}ms` }}
              >
                <StepCard step={step} index={index} t={t} />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Platform Features Section */}
      <section ref={streamsRef} className="py-20 relative" style={{
        background: 'linear-gradient(135deg, #FFD302 0%, #FC8E03 100%)'
      }}>
        {/* Dynamic scattered BONK background images - Enhanced with more variations */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Original scattered elements */}
          <div className="absolute top-12 right-12 w-28 h-28 opacity-25 -rotate-20 animate-pulse">
            <Image src="/WhiteOutline.png" alt="" fill className="object-contain" />
          </div>
          <div className="absolute top-1/4 left-12 w-32 h-32 opacity-18 rotate-75">
            <Image src="/WhiteOutline.png" alt="" fill className="object-contain" />
          </div>
          <div className="absolute bottom-16 right-1/4 w-24 h-24 opacity-32 -rotate-60">
            <Image src="/WhiteOutline.png" alt="" fill className="object-contain" />
          </div>
          <div className="absolute bottom-8 left-16 w-36 h-36 opacity-20 rotate-45">
            <Image src="/WhiteOutline.png" alt="" fill className="object-contain" />
          </div>
          <div className="absolute top-16 left-1/3 w-20 h-20 opacity-38 rotate-120 animate-pulse">
            <Image src="/WhiteOutline.png" alt="" fill className="object-contain" />
          </div>
          <div className="absolute bottom-1/4 right-16 w-26 h-26 opacity-28 -rotate-90">
            <Image src="/WhiteOutline.png" alt="" fill className="object-contain" />
          </div>
          <div className="absolute top-1/3 right-1/3 w-18 h-18 opacity-35 rotate-30 animate-pulse">
            <Image src="/WhiteOutline.png" alt="" fill className="object-contain" />
          </div>
          <div className="absolute bottom-12 left-1/3 w-30 h-30 opacity-22 -rotate-135">
            <Image src="/WhiteOutline.png" alt="" fill className="object-contain" />
          </div>
          {/* Additional scattered WhiteOutline elements */}
          <div className="absolute top-6 left-6 w-22 h-22 opacity-30 rotate-15 animate-pulse">
            <Image src="/WhiteOutline.png" alt="" fill className="object-contain" />
          </div>
          <div className="absolute top-2/3 left-8 w-28 h-28 opacity-26 -rotate-105">
            <Image src="/WhiteOutline.png" alt="" fill className="object-contain" />
          </div>
          <div className="absolute top-8 right-1/4 w-16 h-16 opacity-33 rotate-80 animate-pulse">
            <Image src="/WhiteOutline.png" alt="" fill className="object-contain" />
          </div>
          <div className="absolute bottom-6 right-6 w-20 h-20 opacity-29 -rotate-25">
            <Image src="/WhiteOutline.png" alt="" fill className="object-contain" />
          </div>
          <div className="absolute top-1/2 left-2 w-24 h-24 opacity-31 rotate-165">
            <Image src="/WhiteOutline.png" alt="" fill className="object-contain" />
          </div>
          <div className="absolute bottom-1/3 left-1/4 w-26 h-26 opacity-27 -rotate-75 animate-pulse">
            <Image src="/WhiteOutline.png" alt="" fill className="object-contain" />
          </div>
          <div className="absolute top-1/2 right-2 w-30 h-30 opacity-24 rotate-50">
            <Image src="/WhiteOutline.png" alt="" fill className="object-contain" />
          </div>
          <div className="absolute bottom-4 right-1/3 w-18 h-18 opacity-36 -rotate-160 animate-pulse">
            <Image src="/WhiteOutline.png" alt="" fill className="object-contain" />
          </div>
        </div>
        <div className="absolute inset-0 bg-gradient-to-br from-bonk-yellow/35 to-bonk-orange/35"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className={`text-center mb-12 transition-all duration-1000 ${streamsInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <h2 className="bonk-header text-4xl md:text-6xl text-black mb-4">
              {textContent.platformFeatures}
            </h2>
            <div className="w-24 h-1 bg-bonk-red mx-auto rounded-full shadow-lg"></div>
              <div className="bonk-badge mb-4 mt-4 text-white bg-bonk-red border-transparent hover:bg-bonk-red/90">
                FULL STREAMING ON DESKTOP & MOBILE
              </div>
          </div>

          {/* Feature Cards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {[
              {
                key: 'transparent_payments',
                icon: LinkIcon,
                title: textContent.features.transparent_payments,
                desc: textContent.features.transparent_payments_desc
              },
              {
                key: 'direct_monetization',
                icon: Coins,
                title: textContent.features.direct_monetization,
                desc: textContent.features.direct_monetization_desc
              },
              {
                key: 'no_ads',
                icon: Shield,
                title: textContent.features.no_ads,
                desc: textContent.features.no_ads_desc
              },
              {
                key: 'no_moderation',
                icon: Users,
                title: textContent.features.no_moderation,
                desc: textContent.features.no_moderation_desc
              },
              {
                key: 'anonymous_streaming',
                icon: UserX,
                title: textContent.features.anonymous_streaming,
                desc: textContent.features.anonymous_streaming_desc
              },
              {
                key: 'no_manipulation',
                icon: BarChart3,
                title: textContent.features.no_manipulation,
                desc: textContent.features.no_manipulation_desc
              },
              {
                key: 'instant_payouts',
                icon: Bolt,
                title: textContent.features.instant_payouts,
                desc: textContent.features.instant_payouts_desc
              },
              {
                key: 'fresh_content',
                icon: Flame,
                title: textContent.features.fresh_content,
                desc: textContent.features.fresh_content_desc
              }
            ].map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <div
                  key={feature.key}
                  className={`bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-1000 group hover:scale-105 hover:shadow-bonk-xl ${streamsInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}
                  style={{ animationDelay: `${index * 80}ms` }}
                >
                  <div className="flex justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                    <IconComponent className="w-12 h-12 text-bonk-orange" />
                  </div>
                  <h3 className="text-xl font-herborn font-bold text-white mb-3 text-center">
                    {feature.title}
                  </h3>
                  <p className="text-white/80 text-sm leading-relaxed text-center">
                    {feature.desc}
                  </p>
                </div>
              );
            })}
          </div>

          {/* Animated Feature Marquee */}
          <div className="overflow-hidden bg-black/20 rounded-xl p-4 backdrop-blur-sm">
            <div className="animate-marquee whitespace-nowrap">
              {/* First set of content */}
              <span className="text-white font-helvetica text-lg mx-12 inline-block">
                <FormattedText text={textContent.appFeatures.current} />
              </span>
              <span className="text-white font-helvetica text-lg mx-12 inline-block">
                <FormattedText text={textContent.appFeatures.coming_soon} />
              </span>
              <span className="text-white font-helvetica text-lg mx-12 inline-block">
                <FormattedText text={textContent.appFeatures.interactions} />
              </span>
              {/* Duplicate set for seamless loop */}
              <span className="text-white font-helvetica text-lg mx-12 inline-block">
                <FormattedText text={textContent.appFeatures.current} />
              </span>
              <span className="text-white font-helvetica text-lg mx-12 inline-block">
                <FormattedText text={textContent.appFeatures.coming_soon} />
              </span>
              <span className="text-white font-helvetica text-lg mx-12 inline-block">
                <FormattedText text={textContent.appFeatures.interactions} />
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section ref={ctaRef} className="py-20 bg-bonk-gradient-cta relative overflow-hidden">
        {/* Add Wave.png as background behind the CTA */}
        <div className="absolute inset-0 z-0">
          <Image
            src="/Wave.png"
            alt=""
            fill
            className="object-cover opacity-30"
            sizes="100vw"
          />
        </div>
        <div className="absolute inset-0 bg-bonk-gradient-cta/80"></div>
        <div className="container mx-auto px-4 text-center relative z-10">
          <div className={`transition-all duration-1000 ${ctaInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <h2 className="text-4xl md:text-6xl font-herborn font-black text-white mb-6">
              {textContent.cta.joinRevolution}
            </h2>
            <p className="text-xl font-helvetica text-white/90 mb-8 max-w-2xl mx-auto leading-relaxed">
              {textContent.cta.startStreaming}
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/streams">
                <button className="bonk-btn text-lg px-8 py-4 font-bold shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 bg-bonk-orange hover:bg-bonk-red text-white rounded-lg inline-flex items-center justify-center h-14">
                  <TrendingUp className="w-5 h-5 mr-2" />
                  Explore Streams
                </button>
              </Link>
              <Link href="/creator/register">
                <button className="border-white text-white hover:bg-white/20 hover:text-white text-lg px-8 py-4 font-bold shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border-2 rounded-lg inline-flex items-center justify-center h-14">
                  <Users className="w-5 h-5 mr-2" />
                  Start Creating
                </button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* About BONK Section */}
      <section ref={aboutRef} className="py-20 relative" style={{
        background: 'linear-gradient(135deg, #2B3849 0%, #000205 100%)'
      }}>
        <div className="container mx-auto px-4 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">

            <div className={`transition-all duration-1000 ${aboutInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
              <div className="flex items-center gap-3 mb-6">
                <h2 className="text-4xl md:text-5xl font-herborn font-black text-white">
                  {textContent.about.title}
                </h2>
                {/* Replace emoji placeholder with StandAloneHead.png */}
                <div className="w-12 h-12 relative">
                  <Image
                    src="/StandAloneHead.png"
                    alt="BONK Head"
                    width={48}
                    height={48}
                    className="object-contain"
                  />
                </div>
              </div>
              
              <p className="text-lg text-white/90 mb-8 leading-relaxed font-helvetica">
                {textContent.about.description}
              </p>

              {/* Add Magnify.png between text and numbers */}
              <div className="flex justify-center mb-6">
                <div className="w-150 h-150 relative">
                  <Image
                    src="/Magnify.png"
                    alt="Magnify"
                    width={150}
                    height={150}
                    className="object-contain"
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-6 mb-8">
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-herborn font-black text-bonk-orange mb-2">
                    943K
                  </div>
                  <div className="text-sm text-white/70 font-helvetica uppercase tracking-wide">
                    {textContent.about.holders}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-herborn font-black text-bonk-orange mb-2">
                    400
                  </div>
                  <div className="text-sm text-white/70 font-helvetica uppercase tracking-wide">
                    {textContent.about.integrations}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-herborn font-black text-bonk-orange mb-2">
                    13
                  </div>
                  <div className="text-sm text-white/70 font-helvetica uppercase tracking-wide">
                    {textContent.about.chains}
                  </div>
                </div>
              </div>
            </div>

            <div className={`flex justify-center lg:justify-end transition-all duration-1000 ${aboutInView ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4'}`} style={{ transitionDelay: '200ms' }}>
              <div className="w-full">
                  <BonkPriceCard />
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}