import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // Use shared admin authentication
    const { verifyAdminAuth } = await import('@/lib/admin-auth');
    const authResult = await verifyAdminAuth(request);
    
    if (!authResult.isValid) {
      return authResult.error!;
    }
    
    const supabase = await createClient();
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }
    
    // Get URL parameters for pagination and filtering
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const search = searchParams.get('search') || '';
    const role = searchParams.get('role') || 'all';
    
    const offset = (page - 1) * limit;
    
    // Build query
    let query = supabase
      .from('profiles')
      .select('*', { count: 'exact' });
    
    // Apply search filter
    if (search) {
      query = query.or(`username.ilike.%${search}%,wallet_address.ilike.%${search}%`);
    }
    
    // Apply role filter
    if (role === 'admin') {
      query = query.eq('is_admin', true);
    } else if (role === 'creator') {
      query = query.eq('is_creator', true);
    } else if (role === 'user') {
      query = query.eq('is_admin', false).eq('is_creator', false);
    }
    
    // Apply status filter (assuming active/suspended is stored in a status field)
    // For now, we'll assume all users are active since we don't have a status field
    // if (status === 'active') {
    //   query = query.eq('status', 'active');
    // } else if (status === 'suspended') {
    //   query = query.eq('status', 'suspended');
    // }
    
    // Apply pagination
    query = query.range(offset, offset + limit - 1);
    
    // Order by creation date
    query = query.order('created_at', { ascending: false });
    
    const { data: users, error, count } = await query;
    
    if (error) {
      console.error('Error fetching users:', error);
      return NextResponse.json(
        { error: 'Failed to fetch users' },
        { status: 500 }
      );
    }
    
    // Transform data to include status (all users are active for now)
    const transformedUsers = users?.map(user => ({
      ...user,
      status: 'active', // Default status since we don't have a status field yet
      last_active: user.updated_at || user.created_at
    })) || [];
    
    return NextResponse.json({
      success: true,
      data: {
        users: transformedUsers,
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error in users API:', error);
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
} 