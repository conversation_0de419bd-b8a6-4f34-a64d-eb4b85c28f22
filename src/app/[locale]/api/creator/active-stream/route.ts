import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const walletAddress = searchParams.get('wallet');
    
    if (!walletAddress) {
      return NextResponse.json(
        { error: 'Wallet address is required' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const supabase = await createClient();
    if (!supabase) {
      throw new Error('Failed to create Supabase client');
    }

    // Get the user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, is_creator')
      .eq('wallet_address', walletAddress)
      .single();

    if (profileError) {
      if (profileError.code === 'PGRST116') {
        return NextResponse.json(
          { hasActiveStream: false, activeStream: null },
          { status: 200 }
        );
      }
      throw profileError;
    }

    if (!profile || !profile.is_creator) {
      return NextResponse.json(
        { hasActiveStream: false, activeStream: null },
        { status: 200 }
      );
    }

    // Check for active streams that are actually broadcasting
    const { data: activeStreams, error: streamsError } = await supabase
      .from('streams')
      .select('id, title, status, livepeer_stream_id, livepeer_playback_id, created_at, is_active')
      .eq('creator_id', profile.id)
      .eq('status', 'live')
      .eq('is_active', true)  // Only consider streams that are actually broadcasting
      .order('created_at', { ascending: false })
      .limit(1);

    if (streamsError) {
      throw streamsError;
    }

    // Additional check: verify stream is actually broadcasting via Livepeer
    let hasActiveStream = false;
    let activeStream = null;

    if (activeStreams && activeStreams.length > 0) {
      const stream = activeStreams[0];
      
      // For now, trust the database is_active flag
      // In the future, we could add Livepeer API check here
      hasActiveStream = true;
      activeStream = stream;
    }

    return NextResponse.json({
      hasActiveStream,
      activeStream
    });

  } catch (error) {
    console.error('Error checking for active stream:', error);
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
} 